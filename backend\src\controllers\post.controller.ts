import { Request, Response } from 'express';
import mongoose from 'mongoose';
import { Report } from '../models/Report';
import { notifyNewComment } from '../services/notification.service';

// Create Post model directly to avoid import issues
const Schema = mongoose.Schema;

const ReactionSchema = new Schema({
  user: { type: Schema.Types.ObjectId, ref: 'User', required: true },
  type: { type: String, enum: ['like', 'love', 'haha', 'sad', 'angry'], required: true }
}, { _id: false });

const PostSchema = new Schema({
  user: { type: Schema.Types.ObjectId, ref: 'User', required: true },
  content: { type: String, required: true },
  media: [{ type: String }],
  reactions: [ReactionSchema],
  comments: [{ type: Schema.Types.ObjectId, ref: 'Comment' }],
  reports: [{ type: Schema.Types.ObjectId, ref: 'Report' }],
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now }
});

const Post = mongoose.models.Post || mongoose.model('Post', PostSchema);

// Comment model
const CommentSchema = new Schema({
  user: { type: Schema.Types.ObjectId, ref: 'User', required: true },
  post: { type: Schema.Types.ObjectId, ref: 'Post', required: true },
  content: { type: String, required: true },
  createdAt: { type: Date, default: Date.now }
});

const Comment = mongoose.models.Comment || mongoose.model('Comment', CommentSchema);

// Report model is imported from ../models/Report

// User model for populate
const UserSchema = new Schema({
  name: { type: String, required: true },
  email: { type: String, required: true, unique: true },
  password: { type: String, required: true },
  avatar: { type: String, default: '' },
  role: { type: String, enum: ['user', 'admin'], default: 'user' },
  createdAt: { type: Date, default: Date.now }
});

const User = mongoose.models.User || mongoose.model('User', UserSchema);

// Models are now properly initialized

export const createPost = async (req: Request, res: Response) => {
  try {
    const { content } = req.body;

    if (!content || content.trim() === '') {
      return res.status(400).json({ success: false, message: 'Content is required' });
    }

    const post = await Post.create({
      user: (req as any).user?._id,
      content: content.trim(),
      media: [] // Tạm thời không hỗ trợ media
    });

    // Populate user information để frontend không cần gọi API thêm
    const populatedPost = await Post.findById(post._id).populate('user', 'name email avatar');

    res.json({ success: true, post: populatedPost });
  } catch (err: any) {
    console.error('CREATE POST ERROR:', err);
    res.status(500).json({ success: false, message: err.message });
  }
};

export const deletePost = async (req: Request, res: Response) => {
  try {
    const post = await Post.findById(req.params.id);
    if (!post) return res.status(404).json({ message: 'Post not found' });
    if (post.user.toString() !== (req as any).user?._id.toString() && (req as any).user?.role !== 'admin') {
      return res.status(403).json({ message: 'Not allowed' });
    }
    await post.remove();
    res.json({ success: true });
  } catch (err: any) {
    res.status(500).json({ success: false, message: err.message });
  }
};

export const getPosts = async (req: Request, res: Response) => {
  try {
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;
    const posts = await Post.find()
      .sort({ createdAt: -1 })
      .skip((page - 1) * limit)
      .limit(limit)
      .populate('user', 'name avatar')
      .populate({ path: 'comments', populate: { path: 'user', select: 'name avatar' } });
    res.json({ success: true, posts });
  } catch (err: any) {
    res.status(500).json({ success: false, message: err.message });
  }
};

export const reactPost = async (req: Request, res: Response) => {
  try {
    const { type } = req.body;
    const post = await Post.findById(req.params.id);
    if (!post) return res.status(404).json({ message: 'Post not found' });
    post.reactions = post.reactions.filter((r: any) => r.user.toString() !== (req as any).user?._id.toString());
    post.reactions.push({ user: (req as any).user?._id, type });
    await post.save();
    res.json({ success: true, reactions: post.reactions });
  } catch (err: any) {
    res.status(500).json({ success: false, message: err.message });
  }
};

export const commentPost = async (req: Request, res: Response) => {
  try {
    const { content } = req.body;
    const post = await Post.findById(req.params.id).populate('user', 'name');
    if (!post) return res.status(404).json({ message: 'Post not found' });

    const comment = await (Comment as any).create({
      user: (req as any).user?._id,
      post: post._id,
      content
    });

    post.comments.push(comment._id);
    await post.save();

    // Send notification to post owner (if not commenting on own post)
    const commenterId = (req as any).user?._id;
    const postOwnerId = post.user._id || post.user;

    if (commenterId && postOwnerId && commenterId.toString() !== postOwnerId.toString()) {
      try {
        const commenterUser = await User.findById(commenterId).select('name');
        if (commenterUser) {
          await notifyNewComment(postOwnerId, commenterUser.name, post._id);
          console.log('📧 [Comment] Notification sent to post owner');
        }
      } catch (notificationError) {
        console.error('❌ [Comment] Error sending notification:', notificationError);
        // Don't fail the comment creation if notification fails
      }
    }

    res.json({ success: true, comment });
  } catch (err: any) {
    res.status(500).json({ success: false, message: err.message });
  }
};

export const reportPost = async (req: Request, res: Response) => {
  try {
    console.log('🚨 Report request received:', {
      postId: req.params.id,
      reason: req.body.reason,
      userId: (req as any).user?._id
    });

    const { reason } = req.body;

    if (!reason || reason.trim() === '') {
      console.log('❌ Missing reason');
      return res.status(400).json({ success: false, message: 'Lý do báo cáo là bắt buộc' });
    }

    const post = await Post.findById(req.params.id);
    if (!post) {
      console.log('❌ Post not found:', req.params.id);
      return res.status(404).json({ success: false, message: 'Không tìm thấy bài viết' });
    }

    console.log('✅ Post found:', post._id);

    const existed = await (Report as any).findOne({ user: (req as any).user?._id, post: post._id });
    if (existed) {
      console.log('❌ Already reported by user:', (req as any).user?._id);
      return res.status(400).json({ success: false, message: 'Bạn đã báo cáo bài viết này rồi' });
    }

    console.log('✅ Creating new report...');
    const report = await (Report as any).create({
      user: (req as any).user?._id,
      post: post._id,
      reason: reason.trim()
    });

    console.log('✅ Report created:', report._id);

    post.reports.push(report._id);
    await post.save();

    console.log('✅ Post updated with report');

    // Create notification for admins
    try {
      const { createReportNotificationForAdmins } = await import('../services/notification.service');
      const reporterUser = await User.findById((req as any).user?._id).select('name');
      const reporterName = reporterUser?.name || 'Người dùng';

      await createReportNotificationForAdmins(
        report._id,
        post._id,
        reporterName,
        reason
      );
      console.log('✅ Admin notification created');
    } catch (notificationError) {
      console.error('❌ Error creating report notification:', notificationError);
      // Don't fail the report if notification fails
    }

    console.log('✅ Report successful, sending response');
    res.json({ success: true, message: 'Đã báo cáo bài viết thành công', report });
  } catch (err: any) {
    console.error('❌ Report error:', err);
    res.status(500).json({ success: false, message: 'Lỗi khi báo cáo bài viết', error: err.message });
  }
};