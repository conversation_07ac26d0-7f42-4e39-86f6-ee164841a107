import express from 'express';
import dotenv from 'dotenv';
import connectDB from './config/db'; // <PERSON><PERSON><PERSON> định file kết nối DB
import notificationRoutes from './routes/notificationRoutes';
import authRoutes from './routes/auth.routes'; // <PERSON><PERSON><PERSON> định auth routes
import campaignRoutes from './routes/campaignRoutes'; // Import campaign routes
import donationRoutes from './routes/donationRoutes'; // Giả định donation routes
import adminCategoriesRoutes from './routes/admin-categories';

dotenv.config();
connectDB(); // Kết nối DB

const app = express();

app.use(express.json()); // Body parser cho JSON
app.use(express.urlencoded({ extended: true })); // Body parser cho URL-encoded data

// Các routes của ứng dụng
app.use('/api/auth', authRoutes);
app.use('/api/notifications', notificationRoutes);
app.use('/api/campaigns', campaignRoutes); // Sử dụng campaign routes
app.use('/api/donations', donationRoutes); // Sử dụng donation routes
app.use('/api/admin', adminCategoriesRoutes); // Temporary admin routes

// Middleware xử lý lỗi (tùy chọn, bạn có thể có middleware riêng)
// app.use(notFound)
// app.use(errorHandler)

export default app; 