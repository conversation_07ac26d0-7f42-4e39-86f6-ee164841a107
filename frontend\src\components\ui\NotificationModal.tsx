import React, { useState, useEffect } from 'react';
import { X, Bell, Clock, CheckCircle, AlertCircle, Info, MessageSquare, Calendar, DollarSign, Settings, User } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import { formatDistanceToNow } from 'date-fns';
import { vi } from 'date-fns/locale';

interface Notification {
  _id: string;
  type: 'donation' | 'campaign' | 'event' | 'post' | 'comment' | 'system' | 'report' | 'admin';
  title: string;
  message: string;
  data?: {
    campaignId?: string;
    donationId?: string;
    eventId?: string;
    postId?: string;
    commentId?: string;
    reportId?: string;
    amount?: number;
    status?: string;
    reason?: string;
    actionType?: 'created' | 'updated' | 'deleted' | 'cancelled' | 'completed' | 'reminder';
    relatedUserId?: string;
    relatedUserName?: string;
  };
  isRead: boolean;
  emailSent: boolean;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  createdAt: string;
  updatedAt: string;
}

interface NotificationModalProps {
  isOpen: boolean;
  onClose: () => void;
  notifications: Notification[];
  onMarkAsRead: (notificationId: string) => void;
  onMarkAllAsRead: () => void;
  loading: boolean;
}

const NotificationModal: React.FC<NotificationModalProps> = ({
  isOpen,
  onClose,
  notifications,
  onMarkAsRead,
  onMarkAllAsRead,
  loading
}) => {
  const [selectedNotification, setSelectedNotification] = useState<Notification | null>(null);

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'donation':
        return <DollarSign className="w-5 h-5 text-green-500" />;
      case 'campaign':
        return <Bell className="w-5 h-5 text-blue-500" />;
      case 'event':
        return <Calendar className="w-5 h-5 text-purple-500" />;
      case 'post':
        return <MessageSquare className="w-5 h-5 text-indigo-500" />;
      case 'comment':
        return <MessageSquare className="w-5 h-5 text-teal-500" />;
      case 'system':
        return <Settings className="w-5 h-5 text-gray-500" />;
      case 'admin':
        return <User className="w-5 h-5 text-red-500" />;
      case 'report':
        return <AlertCircle className="w-5 h-5 text-orange-500" />;
      default:
        return <Info className="w-5 h-5 text-gray-500" />;
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent':
        return 'border-l-red-500 bg-red-50';
      case 'high':
        return 'border-l-orange-500 bg-orange-50';
      case 'medium':
        return 'border-l-blue-500 bg-blue-50';
      case 'low':
        return 'border-l-gray-500 bg-gray-50';
      default:
        return 'border-l-gray-500 bg-gray-50';
    }
  };

  const handleNotificationClick = (notification: Notification) => {
    if (!notification.isRead) {
      onMarkAsRead(notification._id);
    }
    setSelectedNotification(notification);
  };

  const unreadCount = notifications.filter(n => !n.isRead).length;

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      <div className="fixed inset-0 z-50 flex items-center justify-center">
        {/* Backdrop */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="absolute inset-0 bg-black bg-opacity-50"
          onClick={onClose}
        />

        {/* Modal */}
        <motion.div
          initial={{ opacity: 0, scale: 0.95, y: 20 }}
          animate={{ opacity: 1, scale: 1, y: 0 }}
          exit={{ opacity: 0, scale: 0.95, y: 20 }}
          className="relative w-full max-w-4xl max-h-[90vh] mx-4 bg-white rounded-xl shadow-2xl overflow-hidden"
        >
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b border-gray-200 bg-gradient-to-r from-blue-500 to-purple-600 text-white">
            <div className="flex items-center space-x-3">
              <Bell className="w-6 h-6" />
              <h2 className="text-xl font-bold">Thông báo</h2>
              {unreadCount > 0 && (
                <span className="px-2 py-1 text-xs bg-red-500 text-white rounded-full">
                  {unreadCount} mới
                </span>
              )}
            </div>
            <div className="flex items-center space-x-3">
              {unreadCount > 0 && (
                <button
                  onClick={onMarkAllAsRead}
                  className="px-3 py-1 text-sm bg-white bg-opacity-20 hover:bg-opacity-30 rounded-lg transition-colors"
                >
                  Đánh dấu tất cả đã đọc
                </button>
              )}
              <button
                onClick={onClose}
                className="p-1 hover:bg-white hover:bg-opacity-20 rounded-lg transition-colors"
              >
                <X className="w-5 h-5" />
              </button>
            </div>
          </div>

          <div className="flex h-[calc(90vh-120px)]">
            {/* Notifications List */}
            <div className="w-1/2 border-r border-gray-200 overflow-y-auto">
              {loading ? (
                <div className="flex items-center justify-center h-32">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                </div>
              ) : notifications.length === 0 ? (
                <div className="flex flex-col items-center justify-center h-32 text-gray-500">
                  <Bell className="w-12 h-12 mb-2 opacity-50" />
                  <p>Không có thông báo nào</p>
                </div>
              ) : (
                <div className="divide-y divide-gray-100">
                  {notifications.map((notification) => (
                    <motion.div
                      key={notification._id}
                      whileHover={{ backgroundColor: '#f8fafc' }}
                      className={`p-4 cursor-pointer border-l-4 ${getPriorityColor(notification.priority)} ${
                        !notification.isRead ? 'bg-blue-50' : 'bg-white'
                      } ${selectedNotification?._id === notification._id ? 'ring-2 ring-blue-500' : ''}`}
                      onClick={() => handleNotificationClick(notification)}
                    >
                      <div className="flex items-start space-x-3">
                        <div className="flex-shrink-0 mt-1">
                          {getNotificationIcon(notification.type)}
                        </div>
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center justify-between">
                            <p className={`text-sm font-medium ${!notification.isRead ? 'text-gray-900' : 'text-gray-700'}`}>
                              {notification.title}
                            </p>
                            {!notification.isRead && (
                              <div className="w-2 h-2 bg-blue-500 rounded-full flex-shrink-0"></div>
                            )}
                          </div>
                          <p className="text-sm text-gray-600 mt-1 line-clamp-2">
                            {notification.message}
                          </p>
                          <div className="flex items-center justify-between mt-2">
                            <span className="text-xs text-gray-400">
                              {formatDistanceToNow(new Date(notification.createdAt), { 
                                addSuffix: true, 
                                locale: vi 
                              })}
                            </span>
                            <span className={`text-xs px-2 py-1 rounded-full ${
                              notification.priority === 'urgent' ? 'bg-red-100 text-red-800' :
                              notification.priority === 'high' ? 'bg-orange-100 text-orange-800' :
                              notification.priority === 'medium' ? 'bg-blue-100 text-blue-800' :
                              'bg-gray-100 text-gray-800'
                            }`}>
                              {notification.priority}
                            </span>
                          </div>
                        </div>
                      </div>
                    </motion.div>
                  ))}
                </div>
              )}
            </div>

            {/* Notification Detail */}
            <div className="w-1/2 p-6 overflow-y-auto">
              {selectedNotification ? (
                <motion.div
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  className="space-y-4"
                >
                  <div className="flex items-start space-x-3">
                    <div className="flex-shrink-0">
                      {getNotificationIcon(selectedNotification.type)}
                    </div>
                    <div className="flex-1">
                      <h3 className="text-lg font-semibold text-gray-900">
                        {selectedNotification.title}
                      </h3>
                      <div className="flex items-center space-x-2 mt-1">
                        <span className={`text-xs px-2 py-1 rounded-full ${
                          selectedNotification.priority === 'urgent' ? 'bg-red-100 text-red-800' :
                          selectedNotification.priority === 'high' ? 'bg-orange-100 text-orange-800' :
                          selectedNotification.priority === 'medium' ? 'bg-blue-100 text-blue-800' :
                          'bg-gray-100 text-gray-800'
                        }`}>
                          {selectedNotification.priority}
                        </span>
                        <span className="text-xs text-gray-500">
                          {formatDistanceToNow(new Date(selectedNotification.createdAt), { 
                            addSuffix: true, 
                            locale: vi 
                          })}
                        </span>
                        {selectedNotification.isRead && (
                          <CheckCircle className="w-4 h-4 text-green-500" />
                        )}
                      </div>
                    </div>
                  </div>

                  <div className="bg-gray-50 rounded-lg p-4">
                    <p className="text-gray-700 leading-relaxed">
                      {selectedNotification.message}
                    </p>
                  </div>

                  {selectedNotification.data?.reason && (
                    <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                      <h4 className="font-medium text-yellow-800 mb-2">Lý do:</h4>
                      <p className="text-yellow-700">{selectedNotification.data.reason}</p>
                    </div>
                  )}

                  {selectedNotification.data?.relatedUserName && (
                    <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                      <h4 className="font-medium text-blue-800 mb-2">Liên quan đến:</h4>
                      <p className="text-blue-700">{selectedNotification.data.relatedUserName}</p>
                    </div>
                  )}

                  <div className="text-xs text-gray-500 pt-4 border-t border-gray-200">
                    <p>Loại: {selectedNotification.type}</p>
                    <p>Thời gian tạo: {new Date(selectedNotification.createdAt).toLocaleString('vi-VN')}</p>
                    {selectedNotification.emailSent && (
                      <p className="text-green-600">✓ Đã gửi email thông báo</p>
                    )}
                  </div>
                </motion.div>
              ) : (
                <div className="flex flex-col items-center justify-center h-full text-gray-500">
                  <MessageSquare className="w-16 h-16 mb-4 opacity-50" />
                  <p className="text-lg font-medium">Chọn thông báo để xem chi tiết</p>
                  <p className="text-sm">Click vào thông báo bên trái để xem nội dung đầy đủ</p>
                </div>
              )}
            </div>
          </div>
        </motion.div>
      </div>
    </AnimatePresence>
  );
};

export default NotificationModal;
