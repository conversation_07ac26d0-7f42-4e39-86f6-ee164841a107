import mongoose, { Document, Schema } from 'mongoose';

export interface INotification extends Document {
  userId: mongoose.Types.ObjectId;
  type: 'donation' | 'campaign' | 'event' | 'post' | 'comment' | 'system' | 'report' | 'admin';
  title: string;
  message: string;
  data?: {
    campaignId?: mongoose.Types.ObjectId;
    donationId?: mongoose.Types.ObjectId;
    eventId?: mongoose.Types.ObjectId;
    postId?: mongoose.Types.ObjectId;
    commentId?: mongoose.Types.ObjectId;
    reportId?: mongoose.Types.ObjectId;
    amount?: number;
    status?: string;
    reason?: string;
    actionType?: 'created' | 'updated' | 'deleted' | 'cancelled' | 'completed' | 'reminder';
    relatedUserId?: mongoose.Types.ObjectId;
    relatedUserName?: string;
  };
  isRead: boolean;
  emailSent: boolean;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  createdAt: Date;
  updatedAt: Date;
}

const notificationSchema = new Schema<INotification>(
  {
    userId: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true,
      index: true
    },
    type: {
      type: String,
      enum: ['donation', 'campaign', 'event', 'post', 'comment', 'system', 'report', 'admin'],
      required: true
    },
    title: {
      type: String,
      required: true
    },
    message: {
      type: String,
      required: true
    },
    data: {
      campaignId: {
        type: Schema.Types.ObjectId,
        ref: 'Campaign'
      },
      donationId: {
        type: Schema.Types.ObjectId,
        ref: 'Donation'
      },
      eventId: {
        type: Schema.Types.ObjectId,
        ref: 'Event'
      },
      postId: {
        type: Schema.Types.ObjectId,
        ref: 'Post'
      },
      commentId: {
        type: Schema.Types.ObjectId,
        ref: 'Comment'
      },
      reportId: {
        type: Schema.Types.ObjectId,
        ref: 'Report'
      },
      amount: Number,
      status: String,
      reason: String,
      actionType: {
        type: String,
        enum: ['created', 'updated', 'deleted', 'cancelled', 'completed', 'reminder']
      },
      relatedUserId: {
        type: Schema.Types.ObjectId,
        ref: 'User'
      },
      relatedUserName: String
    },
    isRead: {
      type: Boolean,
      default: false
    },
    emailSent: {
      type: Boolean,
      default: false
    },
    priority: {
      type: String,
      enum: ['low', 'medium', 'high', 'urgent'],
      default: 'medium'
    }
  },
  {
    timestamps: true
  }
);

// Add indexes for better query performance
notificationSchema.index({ userId: 1, createdAt: -1 });
notificationSchema.index({ userId: 1, isRead: 1 });

// Check if model exists before creating
export const Notification = mongoose.models.Notification || mongoose.model<INotification>('Notification', notificationSchema);