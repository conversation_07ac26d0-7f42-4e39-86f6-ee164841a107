import { Notification, INotification } from '../models/Notification';
import { User } from '../models/user.model';
import { Types } from 'mongoose';
import { sendEmail } from './emailService';
import { io } from '../index';

interface CreateNotificationParams {
  userId: Types.ObjectId;
  type: 'donation' | 'campaign' | 'event' | 'post' | 'comment' | 'system' | 'report' | 'admin';
  title: string;
  message: string;
  data?: {
    campaignId?: Types.ObjectId;
    donationId?: Types.ObjectId;
    eventId?: Types.ObjectId;
    postId?: Types.ObjectId;
    commentId?: Types.ObjectId;
    reportId?: Types.ObjectId;
    amount?: number;
    status?: string;
    reason?: string;
    actionType?: 'created' | 'updated' | 'deleted' | 'cancelled' | 'completed' | 'reminder';
    relatedUserId?: Types.ObjectId;
    relatedUserName?: string;
  };
  priority?: 'low' | 'medium' | 'high' | 'urgent';
  sendEmail?: boolean;
}

export const createNotification = async (params: CreateNotificationParams) => {
  try {
    const notification = new Notification({
      ...params,
      isRead: false,
      emailSent: false,
      priority: params.priority || 'medium'
    });
    await notification.save();

    // Emit real-time notification
    io.to(`user-${params.userId}`).emit('new_notification', notification);

    // Send email if required and user has email notifications enabled
    if (params.sendEmail !== false) {
      await sendNotificationEmail(notification);
    }

    return notification;
  } catch (error) {
    console.error('Error creating notification:', error);
    throw error;
  }
};

// Send notification email
const sendNotificationEmail = async (notification: INotification) => {
  try {
    const user = await User.findById(notification.userId);
    if (!user || !user.preferences?.emailNotifications) {
      return;
    }

    // Determine if this notification type should send email
    const emailTypes = ['campaign', 'event', 'system', 'admin'];
    if (!emailTypes.includes(notification.type)) {
      return;
    }

    await sendEmail({
      to: user.email,
      subject: notification.title,
      template: 'notification',
      data: {
        userName: user.name,
        title: notification.title,
        message: notification.message,
        type: notification.type,
        priority: notification.priority,
        createdAt: notification.createdAt
      }
    });

    // Mark email as sent
    await Notification.findByIdAndUpdate(notification._id, { emailSent: true });
  } catch (error) {
    console.error('Error sending notification email:', error);
  }
};

export const getUnreadNotifications = async (userId: Types.ObjectId) => {
  try {
    return await Notification.find({ userId, isRead: false })
      .sort({ createdAt: -1 })
      .limit(50);
  } catch (error) {
    console.error('Error getting unread notifications:', error);
    throw error;
  }
};

export const markNotificationAsRead = async (notificationId: string, userId: Types.ObjectId) => {
  try {
    return await Notification.findOneAndUpdate(
      { _id: notificationId, userId },
      { isRead: true },
      { new: true }
    );
  } catch (error) {
    console.error('Error marking notification as read:', error);
    throw error;
  }
};

export const createReportNotificationForAdmins = async (reportId: Types.ObjectId, postId: Types.ObjectId, reporterName: string, reason: string) => {
  try {
    // Import User model to find admins
    const { User } = await import('../models/user.model');

    // Find all admin users
    const admins = await User.find({ role: 'admin' }).select('_id');

    // Create notification for each admin
    const notifications = await Promise.all(
      admins.map(admin =>
        createNotification({
          userId: admin._id,
          type: 'report',
          title: 'Báo cáo bài viết mới',
          message: `${reporterName} đã báo cáo một bài viết với lý do: ${reason}`,
          data: {
            reportId,
            postId
          }
        })
      )
    );

    return notifications;
  } catch (error) {
    console.error('Error creating report notifications for admins:', error);
    throw error;
  }
};

export const markAllNotificationsAsRead = async (userId: Types.ObjectId) => {
  try {
    return await Notification.updateMany(
      { userId, isRead: false },
      { isRead: true }
    );
  } catch (error) {
    console.error('Error marking all notifications as read:', error);
    throw error;
  }
};

export const createCommentReportNotificationForAdmins = async (
  reportId: Types.ObjectId,
  commentId: Types.ObjectId,
  postId: Types.ObjectId,
  reporterName: string,
  reason: string
) => {
  try {
    // Import User model to find admins
    const { User } = await import('../models/user.model');

    // Find all admin users
    const admins = await User.find({ role: 'admin' }).select('_id');

    // Create notification for each admin
    const notifications = await Promise.all(
      admins.map(admin =>
        createNotification({
          userId: admin._id,
          type: 'comment_report',
          title: 'Báo cáo comment mới',
          message: `${reporterName} đã báo cáo một comment với lý do: ${reason}`,
          data: {
            reportId,
            commentId,
            postId
          }
        })
      )
    );

    return notifications;
  } catch (error) {
    console.error('Error creating comment report notifications for admins:', error);
    throw error;
  }
};

export const deleteNotification = async (notificationId: string, userId: Types.ObjectId) => {
  try {
    return await Notification.findOneAndDelete({ _id: notificationId, userId });
  } catch (error) {
    console.error('Error deleting notification:', error);
    throw error;
  }
};

// Notification helpers for specific events
export const notifyNewComment = async (postOwnerId: Types.ObjectId, commenterName: string, postId: Types.ObjectId) => {
  return createNotification({
    userId: postOwnerId,
    type: 'comment',
    title: 'Bình luận mới',
    message: `${commenterName} đã bình luận vào bài viết của bạn`,
    data: {
      postId,
      actionType: 'created',
      relatedUserName: commenterName
    },
    priority: 'low',
    sendEmail: false // Don't send email for comments
  });
};

export const notifyCampaignDeleted = async (donorIds: Types.ObjectId[], campaignTitle: string, campaignId: Types.ObjectId, reason?: string) => {
  const notifications = donorIds.map(donorId =>
    createNotification({
      userId: donorId,
      type: 'campaign',
      title: 'Chiến dịch đã bị hủy',
      message: `Chiến dịch "${campaignTitle}" đã bị hủy. Vui lòng liên hệ để được hoàn tiền.${reason ? ` Lý do: ${reason}` : ''}`,
      data: {
        campaignId,
        actionType: 'cancelled',
        reason
      },
      priority: 'high',
      sendEmail: true
    })
  );
  return Promise.all(notifications);
};

export const notifyEventDeleted = async (participantIds: Types.ObjectId[], eventTitle: string, eventId: Types.ObjectId, reason?: string) => {
  const notifications = participantIds.map(participantId =>
    createNotification({
      userId: participantId,
      type: 'event',
      title: 'Sự kiện đã bị hủy',
      message: `Sự kiện "${eventTitle}" đã bị hủy. Bạn không cần tham gia sự kiện này.${reason ? ` Lý do: ${reason}` : ''}`,
      data: {
        eventId,
        actionType: 'cancelled',
        reason
      },
      priority: 'high',
      sendEmail: true
    })
  );
  return Promise.all(notifications);
};

export const notifyPostDeleted = async (userId: Types.ObjectId, postTitle: string, reason: string) => {
  return createNotification({
    userId,
    type: 'post',
    title: 'Bài viết đã bị xóa',
    message: `Bài viết "${postTitle}" của bạn đã bị xóa bởi quản trị viên. Lý do: ${reason}`,
    data: {
      actionType: 'deleted',
      reason
    },
    priority: 'medium',
    sendEmail: false
  });
};

export const notifyCommentDeleted = async (userId: Types.ObjectId, commentContent: string, reason: string) => {
  return createNotification({
    userId,
    type: 'comment',
    title: 'Bình luận đã bị xóa',
    message: `Bình luận "${commentContent.substring(0, 50)}..." của bạn đã bị xóa bởi quản trị viên. Lý do: ${reason}`,
    data: {
      actionType: 'deleted',
      reason
    },
    priority: 'medium',
    sendEmail: false
  });
};

export const notifyNewCampaign = async (userIds: Types.ObjectId[], campaignTitle: string, campaignId: Types.ObjectId) => {
  const notifications = userIds.map(userId =>
    createNotification({
      userId,
      type: 'campaign',
      title: 'Chiến dịch mới',
      message: `Chiến dịch mới "${campaignTitle}" đã được tạo. Hãy tham gia ủng hộ!`,
      data: {
        campaignId,
        actionType: 'created'
      },
      priority: 'medium',
      sendEmail: true
    })
  );
  return Promise.all(notifications);
};

export const notifyNewEvent = async (userIds: Types.ObjectId[], eventTitle: string, eventId: Types.ObjectId) => {
  const notifications = userIds.map(userId =>
    createNotification({
      userId,
      type: 'event',
      title: 'Sự kiện mới',
      message: `Sự kiện mới "${eventTitle}" đã được tạo. Hãy đăng ký tham gia!`,
      data: {
        eventId,
        actionType: 'created'
      },
      priority: 'medium',
      sendEmail: true
    })
  );
  return Promise.all(notifications);
};

export const notifyAdminMessage = async (userIds: Types.ObjectId[], title: string, message: string) => {
  const notifications = userIds.map(userId =>
    createNotification({
      userId,
      type: 'admin',
      title,
      message,
      data: {
        actionType: 'created'
      },
      priority: 'high',
      sendEmail: true
    })
  );
  return Promise.all(notifications);
};

export const notifyDonationSuccess = async (
  donorEmail: string,
  donorName: string,
  amount: number,
  campaignTitle: string,
  campaignId: Types.ObjectId,
  transactionId: string,
  isAnonymous: boolean = false,
  userId?: Types.ObjectId
) => {
  try {
    // Send email notification
    await sendEmail({
      to: donorEmail,
      subject: 'Cảm ơn bạn đã quyên góp - KeyDyWeb',
      template: 'donation-thank-you',
      data: {
        donorName: isAnonymous ? 'Người ủng hộ ẩn danh' : donorName,
        amount: new Intl.NumberFormat('vi-VN', {
          style: 'currency',
          currency: 'VND'
        }).format(amount),
        campaignTitle,
        transactionId,
        donationDate: new Date().toLocaleDateString('vi-VN'),
        isAnonymous
      }
    });

    console.log('📧 [Donation] Thank you email sent to:', donorEmail);

    // Send in-app notification if user is logged in
    if (userId) {
      await createNotification({
        userId,
        type: 'donation',
        title: 'Cảm ơn bạn đã quyên góp!',
        message: `Cảm ơn bạn đã quyên góp ${new Intl.NumberFormat('vi-VN', {
          style: 'currency',
          currency: 'VND'
        }).format(amount)} cho chiến dịch "${campaignTitle}". Sự đóng góp của bạn sẽ tạo nên những thay đổi tích cực!`,
        data: {
          campaignId,
          amount,
          actionType: 'completed',
          status: 'success'
        },
        priority: 'medium',
        sendEmail: false // Already sent email above
      });

      console.log('📱 [Donation] In-app notification sent to user:', userId);
    }
  } catch (error) {
    console.error('❌ [Donation] Error sending thank you notifications:', error);
    throw error;
  }
};