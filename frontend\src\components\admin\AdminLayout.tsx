import React, { useState } from 'react';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import {
  BarChart3,
  FileText,
  MessageSquareWarning,
  Calendar,
  Heart,
  DollarSign,
  Users,
  Menu,
  X,
  Home,
  ChevronLeft,
  Bell
} from 'lucide-react';

interface AdminLayoutProps {
  children: React.ReactNode;
}

const AdminLayout: React.FC<AdminLayoutProps> = ({ children }) => {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const location = useLocation();
  const navigate = useNavigate();

  const menuItems = [
    {
      name: 'Tổng quan',
      href: '/admin/dashboard',
      icon: BarChart3,
      description: 'Dashboard & Analytics'
    },
    {
      name: '<PERSON><PERSON><PERSON> viết',
      href: '/admin/posts',
      icon: FileText,
      description: 'Quản lý bài viết'
    },
    {
      name: 'Báo cáo comment',
      href: '/admin/comment-reports',
      icon: MessageSquareWarning,
      description: '<PERSON><PERSON> lý báo cáo'
    },
    {
      name: '<PERSON><PERSON> kiện',
      href: '/admin/events',
      icon: Calendar,
      description: '<PERSON>u<PERSON><PERSON> lý sự kiện'
    },
    {
      name: 'Chiến dịch',
      href: '/admin/campaigns',
      icon: Heart,
      description: 'Quản lý chiến dịch'
    },
    {
      name: 'Quyên góp',
      href: '/admin/donations',
      icon: DollarSign,
      description: 'Quản lý quyên góp'
    },
    {
      name: 'Thông báo',
      href: '/admin/notifications',
      icon: Bell,
      description: 'Quản lý thông báo'
    },
    {
      name: 'Người dùng',
      href: '/admin/users',
      icon: Users,
      description: 'Quản lý người dùng'
    }
  ];

  const isActive = (href: string) => {
    return location.pathname === href || location.pathname.startsWith(href + '/');
  };

  return (
    <div className="min-h-screen bg-gray-50 flex">
      {/* Mobile sidebar overlay */}
      {sidebarOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden"
          onClick={() => setSidebarOpen(false)}
        />
      )}

      {/* Sidebar */}
      <div className={`
        fixed inset-y-0 left-0 z-50 w-64 bg-gradient-to-b from-purple-600 to-pink-600
        transform transition-transform duration-300 ease-in-out lg:translate-x-0
        ${sidebarOpen ? 'translate-x-0' : '-translate-x-full'}
      `}>
        <div className="flex flex-col h-full">
          {/* Header */}
          <div className="flex items-center justify-between h-16 px-6 bg-black bg-opacity-10 relative">
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-white rounded-lg flex items-center justify-center">
                <BarChart3 className="w-5 h-5 text-purple-600" />
              </div>
              <h1 className="text-xl font-bold text-white">Admin</h1>
            </div>

            {/* Home Sticker */}
            <button
              onClick={() => navigate('/')}
              className="absolute -top-2 -right-2 w-8 h-8 bg-yellow-400 hover:bg-yellow-500
                       rounded-full flex items-center justify-center shadow-lg transform hover:scale-110
                       transition-all duration-200 border-2 border-white"
              title="Về trang chủ"
            >
              <Home className="w-4 h-4 text-gray-800" />
            </button>

            {/* Close button for mobile */}
            <button
              onClick={() => setSidebarOpen(false)}
              className="lg:hidden text-white hover:text-gray-200"
            >
              <X className="w-6 h-6" />
            </button>
          </div>

          {/* Navigation */}
          <nav className="flex-1 px-4 py-6 space-y-2">
            {menuItems.map((item) => {
              const Icon = item.icon;
              const active = isActive(item.href);

              return (
                <Link
                  key={item.name}
                  to={item.href}
                  onClick={() => setSidebarOpen(false)}
                  className={`
                    flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-all duration-200
                    ${active
                      ? 'bg-white bg-opacity-20 text-white shadow-lg'
                      : 'text-purple-100 hover:bg-white hover:bg-opacity-10 hover:text-white'
                    }
                  `}
                >
                  <Icon className="w-5 h-5 mr-3" />
                  <div className="flex-1">
                    <div className="font-medium">{item.name}</div>
                    <div className="text-xs opacity-75">{item.description}</div>
                  </div>
                </Link>
              );
            })}
          </nav>


        </div>
      </div>

      {/* Main content */}
      <div className="flex-1 lg:ml-64">
        {/* Mobile header */}
        <div className="lg:hidden bg-white shadow-sm border-b border-gray-200">
          <div className="flex items-center justify-between h-16 px-4">
            <button
              onClick={() => setSidebarOpen(true)}
              className="text-gray-600 hover:text-gray-900"
            >
              <Menu className="w-6 h-6" />
            </button>
            <h1 className="text-lg font-semibold text-gray-900">Admin Panel</h1>
            <div className="w-6" /> {/* Spacer */}
          </div>
        </div>

        {/* Page content */}
        <main className="flex-1">
          {children}
        </main>
      </div>
    </div>
  );
};

export default AdminLayout;
