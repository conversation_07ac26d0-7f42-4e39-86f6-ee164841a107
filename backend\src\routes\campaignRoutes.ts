import express from 'express';
import { createCampaign, getCampaigns, updateCampaign, deleteCampaign, updateCampaignStatus, getPublicCampaigns, getCampaignById, manualUpdateCampaignStatuses } from '../controllers/campaignController';
import upload from '../middleware/upload';
import { protect, admin } from '../middleware/authMiddleware';

const router = express.Router();

console.log('🔧 [Campaign] Campaign routes loaded');

// Test route
router.get('/admin/test', protect, admin, (req, res) => {
  console.log('🧪 [Campaign] Test route called');
  res.json({ success: true, message: 'Campaign admin test route working', user: req.user?.name });
});

// Admin routes
router.post('/admin/simple', protect, admin, createCampaign); // Simple route without file upload
router.post('/admin', protect, admin, upload.array('images', 5), createCampaign);
router.get('/admin', protect, admin, getCampaigns);
router.put('/admin/:id', protect, admin, upload.array('images', 5), updateCampaign);
router.delete('/admin/:id', protect, admin, deleteCampaign);
router.patch('/admin/:id/status', protect, admin, updateCampaignStatus);
router.post('/admin/update-statuses', protect, admin, manualUpdateCampaignStatuses);

// Public routes
router.get('/', getPublicCampaigns);
router.get('/:id', getCampaignById);

export default router; 