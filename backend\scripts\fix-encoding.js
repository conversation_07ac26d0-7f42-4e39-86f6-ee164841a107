require('dotenv').config();
const mongoose = require('mongoose');
const { Campaign } = require('../dist/models/Campaign');

// Kết nối MongoDB với cấu hình UTF-8
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/keydyweb';
console.log('🔗 Connecting to MongoDB...');

// Mapping để sửa các category bị lỗi encoding
const encodingFixes = {
  'Gi�o d?c': 'Gi<PERSON>o dục',
  'Gi�o duc': 'Gi<PERSON>o dục',
  '<PERSON>ia<PERSON> duc': 'Gi<PERSON><PERSON> dục',
  'Y t?': 'Y tế',
  'Y te': 'Y tế',
  'Tr? em': 'Trẻ em',
  'Tre em': 'Trẻ em',
  'Ng??i già': 'Người già',
  'Nguoi gia': 'Người già',
  'Môi tr??ng': '<PERSON>ôi trường',
  '<PERSON><PERSON> truong': '<PERSON>ôi trường',
  'Thi�n tai': 'Thiên tai',
  'Thien tai': 'Thiên tai',
  'Ð?ng v?t': 'Động vật',
  'Dong vat': 'Động vật',
  'C?ng ð?ng': 'Cộng đồng',
  'Cong dong': 'Cộng đồng',
  'V?n hóa': 'Văn hóa',
  'Van hoa': 'Văn hóa',
  'Th? thao': 'Thể thao',
  'The thao': 'Thể thao'
};

async function fixCategoryEncoding() {
  try {
    // Kết nối MongoDB
    await mongoose.connect(MONGODB_URI);
    console.log('✅ Connected to MongoDB');

    console.log('🔄 Bắt đầu sửa lỗi encoding cho categories...');

    // Lấy tất cả campaigns
    const campaigns = await Campaign.find({});
    console.log(`📊 Tìm thấy ${campaigns.length} chiến dịch`);
    
    if (campaigns.length === 0) {
      console.log('❌ Không có chiến dịch nào để sửa');
      return;
    }

    let fixedCount = 0;
    
    for (const campaign of campaigns) {
      const originalCategory = campaign.category;
      
      // Kiểm tra xem category có bị lỗi encoding không
      if (encodingFixes[originalCategory]) {
        const fixedCategory = encodingFixes[originalCategory];
        
        console.log(`🔧 Sửa category cho campaign "${campaign.title}": "${originalCategory}" -> "${fixedCategory}"`);
        
        // Cập nhật category
        await Campaign.findByIdAndUpdate(
          campaign._id,
          { category: fixedCategory },
          { new: true }
        );
        
        fixedCount++;
      } else if (originalCategory && originalCategory.includes('�') || originalCategory.includes('?')) {
        // Nếu có ký tự lỗi encoding nhưng không có trong mapping
        console.log(`⚠️  Category có vấn đề encoding nhưng chưa có fix: "${originalCategory}" trong campaign "${campaign.title}"`);
      }
    }
    
    console.log(`✅ Đã sửa ${fixedCount} campaign(s)`);
    
    // Hiển thị danh sách categories hiện tại
    const uniqueCategories = await Campaign.distinct('category');
    console.log('📋 Danh sách categories hiện tại:');
    uniqueCategories.forEach(cat => {
      console.log(`  - ${cat}`);
    });
    
  } catch (error) {
    console.error('❌ Lỗi khi sửa encoding:', error);
  } finally {
    console.log('🔌 Đóng kết nối database');
    await mongoose.connection.close();
  }
}

// Chạy script
fixCategoryEncoding();
