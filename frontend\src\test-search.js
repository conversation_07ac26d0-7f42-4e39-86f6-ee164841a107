// Test script để demo tính năng search và category
// Chạy trong browser console

console.log('🔍 Testing Search and Category Features');

// Test search functionality
const testSearch = () => {
  console.log('\n📝 Testing Search...');
  
  // Simulate typing in search box
  const searchInput = document.querySelector('input[placeholder="Tìm kiếm chiến dịch..."]');
  if (searchInput) {
    console.log('✅ Search input found');
    
    // Test search with different terms
    const searchTerms = ['giáo dục', 'y tế', 'trẻ em', 'môi trường'];
    
    searchTerms.forEach((term, index) => {
      setTimeout(() => {
        searchInput.value = term;
        searchInput.dispatchEvent(new Event('input', { bubbles: true }));
        console.log(`🔍 Searching for: "${term}"`);
      }, index * 2000);
    });
    
    // Clear search after tests
    setTimeout(() => {
      searchInput.value = '';
      searchInput.dispatchEvent(new Event('input', { bubbles: true }));
      console.log('🧹 Cleared search');
    }, searchTerms.length * 2000);
    
  } else {
    console.log('❌ Search input not found');
  }
};

// Test category filter
const testCategoryFilter = () => {
  console.log('\n🏷️ Testing Category Filter...');
  
  const categorySelect = document.querySelector('select');
  if (categorySelect) {
    console.log('✅ Category select found');
    console.log('📋 Available categories:', Array.from(categorySelect.options).map(opt => opt.value));
    
    // Test different categories
    const categories = Array.from(categorySelect.options)
      .map(opt => opt.value)
      .filter(val => val !== '');
    
    categories.forEach((category, index) => {
      setTimeout(() => {
        categorySelect.value = category;
        categorySelect.dispatchEvent(new Event('change', { bubbles: true }));
        console.log(`🏷️ Filtering by category: "${category}"`);
      }, (index + 5) * 2000);
    });
    
    // Clear filter after tests
    setTimeout(() => {
      categorySelect.value = '';
      categorySelect.dispatchEvent(new Event('change', { bubbles: true }));
      console.log('🧹 Cleared category filter');
    }, (categories.length + 5) * 2000);
    
  } else {
    console.log('❌ Category select not found');
  }
};

// Test tab switching
const testTabSwitching = () => {
  console.log('\n📑 Testing Tab Switching...');
  
  const tabs = document.querySelectorAll('button[class*="px-6 py-3"]');
  if (tabs.length >= 2) {
    console.log('✅ Tabs found');
    
    // Switch between active and completed
    setTimeout(() => {
      tabs[1].click(); // Completed tab
      console.log('📑 Switched to "Đã kết thúc" tab');
    }, 15000);
    
    setTimeout(() => {
      tabs[0].click(); // Active tab
      console.log('📑 Switched back to "Đang gây quỹ" tab');
    }, 17000);
    
  } else {
    console.log('❌ Tabs not found');
  }
};

// Check campaign cards for category badges
const checkCategoryBadges = () => {
  console.log('\n🏷️ Checking Category Badges...');
  
  const campaignCards = document.querySelectorAll('[class*="bg-white/80 backdrop-blur-md rounded-2xl"]');
  console.log(`📊 Found ${campaignCards.length} campaign cards`);
  
  campaignCards.forEach((card, index) => {
    const categoryBadge = card.querySelector('[class*="bg-gradient-to-r from-purple-500 to-pink-500"]');
    if (categoryBadge) {
      console.log(`✅ Card ${index + 1}: Has category badge - "${categoryBadge.textContent.trim()}"`);
    } else {
      console.log(`❌ Card ${index + 1}: No category badge found`);
    }
  });
};

// Run all tests
const runAllTests = () => {
  console.log('🚀 Starting comprehensive search and category tests...');
  
  // Check initial state
  checkCategoryBadges();
  
  // Run search tests
  setTimeout(testSearch, 1000);
  
  // Run category filter tests
  setTimeout(testCategoryFilter, 10000);
  
  // Run tab switching tests
  setTimeout(testTabSwitching, 20000);
  
  console.log('⏱️ All tests scheduled. Watch the console for results...');
};

// Export functions for manual testing
window.testSearchFeatures = {
  runAllTests,
  testSearch,
  testCategoryFilter,
  testTabSwitching,
  checkCategoryBadges
};

console.log('🎯 Test functions available:');
console.log('- testSearchFeatures.runAllTests() - Run all tests');
console.log('- testSearchFeatures.testSearch() - Test search only');
console.log('- testSearchFeatures.testCategoryFilter() - Test category filter only');
console.log('- testSearchFeatures.testTabSwitching() - Test tab switching');
console.log('- testSearchFeatures.checkCategoryBadges() - Check category badges');

// Auto-run tests after 2 seconds
setTimeout(() => {
  console.log('🔄 Auto-running tests...');
  runAllTests();
}, 2000);
