"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const dotenv_1 = __importDefault(require("dotenv"));
const db_1 = __importDefault(require("./config/db")); // Giả định file kết nối DB
const notificationRoutes_1 = __importDefault(require("./routes/notificationRoutes"));
const auth_routes_1 = __importDefault(require("./routes/auth.routes")); // Giả định auth routes
const campaignRoutes_1 = __importDefault(require("./routes/campaignRoutes")); // Import campaign routes
const donationRoutes_1 = __importDefault(require("./routes/donationRoutes")); // Giả định donation routes
const admin_categories_1 = __importDefault(require("./routes/admin-categories"));
dotenv_1.default.config();
(0, db_1.default)(); // Kết nối DB
const app = (0, express_1.default)();
app.use(express_1.default.json()); // Body parser cho JSON
app.use(express_1.default.urlencoded({ extended: true })); // Body parser cho URL-encoded data
// Các routes của ứng dụng
app.use('/api/auth', auth_routes_1.default);
app.use('/api/notifications', notificationRoutes_1.default);
app.use('/api/campaigns', campaignRoutes_1.default); // Sử dụng campaign routes
app.use('/api/donations', donationRoutes_1.default); // Sử dụng donation routes
app.use('/api/admin', admin_categories_1.default); // Temporary admin routes
// Middleware xử lý lỗi (tùy chọn, bạn có thể có middleware riêng)
// app.use(notFound)
// app.use(errorHandler)
exports.default = app;
