{"css.validate": false, "less.validate": false, "scss.validate": false, "css.lint.unknownAtRules": "ignore", "tailwindCSS.includeLanguages": {"typescript": "javascript", "typescriptreact": "javascript"}, "editor.quickSuggestions": {"strings": true}, "css.customData": [{"version": 1.1, "atDirectives": [{"name": "@tailwind", "description": "Use the @tailwind directive to insert Tailwind's base, components, and utilities styles into your CSS."}, {"name": "@apply", "description": "Use @apply to inline any existing utility classes into your own custom CSS."}, {"name": "@layer", "description": "Use the @layer directive to tell Tailwind which \"bucket\" a set of custom styles belong to."}]}]}