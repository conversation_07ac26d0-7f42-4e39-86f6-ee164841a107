const mongoose = require('mongoose');
const { Campaign } = require('../dist/models/Campaign');

// Kết nối MongoDB
mongoose.connect('mongodb://localhost:27017/charity_db', {
  useNewUrlParser: true,
  useUnifiedTopology: true,
});

const categories = [
  'Gi<PERSON>o dục',
  'Y tế',
  'Trẻ em',
  'Người già',
  'M<PERSON>i trường',
  'Thiên tai',
  'Động vật',
  'C<PERSON>ng đồng',
  '<PERSON>ă<PERSON> hóa',
  'Th<PERSON> thao'
];

async function addCategoriesToCampaigns() {
  try {
    console.log('🔄 Bắt đầu thêm danh mục cho các chiến dịch...');
    
    // Lấy tất cả campaigns
    const campaigns = await Campaign.find({});
    console.log(`📊 Tìm thấy ${campaigns.length} chiến dịch`);
    
    if (campaigns.length === 0) {
      console.log('❌ Không có chiến dịch nào để cập nhật');
      return;
    }
    
    // Cập nhật từng campaign với category ngẫu nhiên
    for (let i = 0; i < campaigns.length; i++) {
      const campaign = campaigns[i];
      
      // Chỉ thêm category nếu chưa có
      if (!campaign.category) {
        const randomCategory = categories[Math.floor(Math.random() * categories.length)];
        
        await Campaign.findByIdAndUpdate(campaign._id, {
          category: randomCategory
        });
        
        console.log(`✅ Cập nhật "${campaign.title}" với danh mục: ${randomCategory}`);
      } else {
        console.log(`⏭️  "${campaign.title}" đã có danh mục: ${campaign.category}`);
      }
    }
    
    console.log('🎉 Hoàn thành thêm danh mục cho tất cả chiến dịch!');
    
    // Hiển thị thống kê
    const updatedCampaigns = await Campaign.find({});
    const categoryStats = {};
    
    updatedCampaigns.forEach(campaign => {
      if (campaign.category) {
        categoryStats[campaign.category] = (categoryStats[campaign.category] || 0) + 1;
      }
    });
    
    console.log('\n📈 Thống kê danh mục:');
    Object.entries(categoryStats).forEach(([category, count]) => {
      console.log(`   ${category}: ${count} chiến dịch`);
    });
    
  } catch (error) {
    console.error('❌ Lỗi khi thêm danh mục:', error);
  } finally {
    mongoose.connection.close();
    console.log('\n🔌 Đã đóng kết nối database');
  }
}

// Chạy script
addCategoriesToCampaigns();
