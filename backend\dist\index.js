"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.io = void 0;
// Load environment variables FIRST
const dotenv_1 = __importDefault(require("dotenv"));
dotenv_1.default.config();
const express_1 = __importDefault(require("express"));
const cors_1 = __importDefault(require("cors"));
const mongoose_1 = __importDefault(require("mongoose"));
const passport_1 = __importDefault(require("passport"));
// Import type definitions
require("./types/express");
const auth_routes_1 = __importDefault(require("./routes/auth.routes"));
const user_routes_1 = __importDefault(require("./routes/user.routes"));
// import charityRoutes from './routes/charity.routes';
const donationRoutes_1 = __importDefault(require("./routes/donationRoutes"));
const home_routes_1 = __importDefault(require("./routes/home.routes"));
const campaignRoutes_1 = __importDefault(require("./routes/campaignRoutes"));
const test_routes_1 = __importDefault(require("./routes/test.routes"));
console.log('🔧 [Server] Campaign routes imported:', typeof campaignRoutes_1.default);
const http_1 = __importDefault(require("http"));
const socket_io_1 = require("socket.io");
const posts_routes_1 = __importDefault(require("./routes/posts.routes"));
const post_routes_1 = __importDefault(require("./routes/post.routes"));
const notificationRoutes_1 = __importDefault(require("./routes/notificationRoutes"));
const admin_routes_1 = __importDefault(require("./routes/admin.routes"));
const event_routes_1 = __importDefault(require("./routes/event.routes"));
// Import cron jobs
const updateCampaignStatus_1 = require("./cron/updateCampaignStatus");
const cleanupDonations_1 = require("./cron/cleanupDonations");
const activity_tracker_1 = require("./utils/activity-tracker");
// Create Express app
const app = (0, express_1.default)();
// Create HTTP server
const server = http_1.default.createServer(app);
// Create Socket.IO server
exports.io = new socket_io_1.Server(server, {
    cors: {
        origin: ['http://localhost:5173', 'http://*********.1:5173', 'http://localhost:5174', 'http://127.0.0.1:5174'],
        methods: ['GET', 'POST']
    }
});
// Socket.IO connection handling
exports.io.on('connection', (socket) => {
    console.log('User connected:', socket.id);
    // Join user to their own room for personal notifications
    socket.on('join-user', (userId) => {
        socket.join(`user-${userId}`);
        console.log(`User ${userId} joined their room`);
    });
    // Handle disconnect
    socket.on('disconnect', () => {
        console.log('User disconnected:', socket.id);
    });
});
// Middleware
app.use(express_1.default.json({ limit: '50mb' }));
app.use(express_1.default.urlencoded({ extended: true, limit: '50mb' }));
// Multer for handling multipart/form-data
const multer_1 = __importDefault(require("multer"));
(0, multer_1.default)({
    dest: 'uploads/',
    limits: { fileSize: 50 * 1024 * 1024 } // 50MB limit
});
// CORS configuration
app.use((0, cors_1.default)({
    origin: [
        'http://localhost:5173', 'http://127.0.0.1:5173',
        'http://localhost:5174', 'http://127.0.0.1:5174',
        'http://localhost:5175', 'http://127.0.0.1:5175',
        'http://localhost:3000', 'http://127.0.0.1:3000'
    ],
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization'],
    credentials: true,
    optionsSuccessStatus: 200
}));
// Initialize Passport
app.use(passport_1.default.initialize());
// Middleware to attach Socket.IO to requests
app.use((req, _res, next) => {
    req.io = exports.io;
    next();
});
// Logging middleware
app.use((req, _res, next) => {
    console.log(`🌐 ${new Date().toISOString()} - ${req.method} ${req.url}`);
    if (req.body && Object.keys(req.body).length > 0) {
        console.log('📝 Request body:', req.body);
    }
    if (req.headers.authorization) {
        console.log('🔑 Auth header present');
    }
    next();
});
// Routes
console.log('🔧 [Server] Mounting routes...');
app.use('/api/auth', auth_routes_1.default);
app.use('/api/users', user_routes_1.default);
app.use('/api/donations', donationRoutes_1.default);
app.use('/api/home', home_routes_1.default);
console.log('🔧 [Server] Mounting campaign routes...');
app.use('/api/campaigns', campaignRoutes_1.default);
app.use('/api/posts', posts_routes_1.default); // New posts routes for newsfeed
app.use('/api/posts', post_routes_1.default); // Post routes with report functionality
app.use('/api/notifications', notificationRoutes_1.default); // Notification routes
app.use('/api/admin', admin_routes_1.default);
app.use('/api/events', event_routes_1.default); // Event routes
app.use('/api/test', test_routes_1.default);
console.log('🔧 [Server] All routes mounted');
// Static folder for uploads
app.use('/uploads', express_1.default.static('uploads'));
// Error handling middleware
const errorHandler = (err, _req, res, _next) => {
    console.error(err.stack);
    res.status(err.status || 500).json({
        message: err.message || 'Internal Server Error',
        error: process.env.NODE_ENV === 'development' ? err : {}
    });
};
app.use(errorHandler);
// Database connection
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/keydyweb';
console.log('🚀 Starting server...');
console.log('📊 MongoDB URI:', MONGODB_URI ? 'Atlas URI configured' : 'Local MongoDB');
mongoose_1.default.connect(MONGODB_URI, {
    serverSelectionTimeoutMS: 5000, // Timeout after 5s instead of 30s
    socketTimeoutMS: 45000,
})
    .then(() => {
    console.log('✅ Connected to MongoDB');
    // Start cron jobs
    console.log('⏰ Starting cron jobs...');
    updateCampaignStatus_1.updateCampaignStatusJob.start();
    cleanupDonations_1.cleanupPendingDonations.start();
    console.log('✅ Cron jobs started');
    // Initialize activity tracking cleanup
    (0, activity_tracker_1.initActivityCleanup)();
    console.log('🟢 Activity tracking initialized');
    // Start server after DB connection
    const PORT = process.env.PORT || 5001;
    server.listen(PORT, () => {
        console.log(`🚀 Server is running on port ${PORT}`);
        console.log('🌐 CORS enabled for:', ['http://localhost:5173', 'http://127.0.0.1:5173', 'http://localhost:5174', 'http://127.0.0.1:5174']);
        console.log('🔌 Socket.IO enabled');
        console.log('⏰ Cron jobs running:');
        console.log('  - Campaign status update (every hour)');
        console.log('  - Cleanup pending donations (every hour)');
        console.log('📋 Available routes:');
        console.log('  - /api/auth');
        console.log('  - /api/campaigns');
        console.log('  - /api/admin');
        console.log('  - /api/posts');
        console.log('  - /api/test');
    });
})
    .catch((error) => {
    console.error('❌ MongoDB connection error:', error);
    process.exit(1);
});
// Handle unhandled promise rejections
process.on('unhandledRejection', (error) => {
    console.error('Unhandled Promise Rejection:', error);
});
// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
    console.error('Uncaught Exception:', error);
    process.exit(1);
});
