<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{title}}</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f4f4f4;
        }
        .container {
            background-color: #ffffff;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #e74c3c;
        }
        .logo {
            font-size: 28px;
            font-weight: bold;
            color: #e74c3c;
            margin-bottom: 10px;
        }
        .notification-type {
            display: inline-block;
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
            text-transform: uppercase;
            margin-bottom: 20px;
        }
        .type-campaign { background-color: #3498db; color: white; }
        .type-event { background-color: #2ecc71; color: white; }
        .type-system { background-color: #f39c12; color: white; }
        .type-admin { background-color: #e74c3c; color: white; }
        .type-post { background-color: #9b59b6; color: white; }
        .type-comment { background-color: #1abc9c; color: white; }
        .priority-urgent { border-left: 5px solid #e74c3c; }
        .priority-high { border-left: 5px solid #f39c12; }
        .priority-medium { border-left: 5px solid #3498db; }
        .priority-low { border-left: 5px solid #95a5a6; }
        .content {
            padding: 20px;
            margin: 20px 0;
            border-radius: 5px;
            background-color: #f8f9fa;
        }
        .title {
            font-size: 24px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 15px;
        }
        .message {
            font-size: 16px;
            line-height: 1.8;
            color: #34495e;
            margin-bottom: 20px;
        }
        .footer {
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #ecf0f1;
            color: #7f8c8d;
            font-size: 14px;
        }
        .button {
            display: inline-block;
            padding: 12px 25px;
            background-color: #e74c3c;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            font-weight: bold;
            margin: 10px 0;
        }
        .button:hover {
            background-color: #c0392b;
        }
        .timestamp {
            color: #7f8c8d;
            font-size: 12px;
            margin-top: 15px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">KeyDyWeb</div>
            <div class="notification-type type-{{type}}">{{type}}</div>
        </div>
        
        <div class="content priority-{{priority}}">
            <div class="title">{{title}}</div>
            <div class="message">
                Xin chào {{userName}},<br><br>
                {{message}}
            </div>
            
            <div class="timestamp">
                Thời gian: {{createdAt}}
            </div>
        </div>
        
        <div style="text-align: center; margin: 20px 0;">
            <a href="http://localhost:5173/notifications" class="button">
                Xem tất cả thông báo
            </a>
        </div>
        
        <div class="footer">
            <p>Đây là email tự động từ hệ thống KeyDyWeb.</p>
            <p>Nếu bạn không muốn nhận email thông báo, vui lòng cập nhật cài đặt trong tài khoản của bạn.</p>
            <p>&copy; 2024 KeyDyWeb. Tất cả quyền được bảo lưu.</p>
        </div>
    </div>
</body>
</html>
