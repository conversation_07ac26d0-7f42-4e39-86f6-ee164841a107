import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>hart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  LineChart,
  Line,
  PieChart,
  Pie,
  Cell,
  AreaChart,
  Area
} from 'recharts';
import CountUp from 'react-countup';
import {
  Users,
  FileText,
  DollarSign,
  Heart,
  TrendingUp,
  AlertTriangle,
  Calendar,
  Target,
  RefreshCw
} from 'lucide-react';
import api from '../../services/api';

const DashboardOverview: React.FC = () => {
  const [stats, setStats] = useState({
    totalUsers: 0,
    totalPosts: 0,
    totalDonations: 0,
    totalCampaigns: 0,
    totalEvents: 0,
    activeReports: 0,
    monthlyGrowth: 0
  });
  const [monthlyData, setMonthlyData] = useState<any[]>([]);
  const [campaignData, setCampaignData] = useState<any[]>([]);
  const [eventData, setEventData] = useState<any[]>([]);
  const [recentActivity, setRecentActivity] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [isUsingRealData, setIsUsingRealData] = useState(false);

  // Fetch dashboard data from API
  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        console.log('🚀 Starting dashboard data fetch...');
        setLoading(true);

        const token = localStorage.getItem('token');
        console.log('🔑 Token:', token ? 'Present' : 'Missing');

        // Try to fetch real data from API
        try {
          console.log('📊 Fetching real dashboard data from API...');
          const response = await api.get('/api/admin/dashboard/stats');
          console.log('✅ Real dashboard data received:', response.data);

          const apiData = response.data.data || response.data;

          // Set real data from API
          setStats(apiData.stats || {
            totalUsers: 0,
            totalPosts: 0,
            totalEvents: 0,
            totalDonations: 0,
            totalCampaigns: 0,
            activeReports: 0,
            monthlyGrowth: 0
          });

          setMonthlyData(apiData.monthlyData || []);
          setCampaignData(apiData.campaignData || []);
          setEventData(apiData.eventData || []);
          setRecentActivity(apiData.recentActivity || []);
          setIsUsingRealData(true);
          console.log('✅ Real dashboard data loaded successfully');

        } catch (apiError: any) {
          console.warn('⚠️ API Error, using fallback data:', apiError);

          // Fallback to basic empty data structure
          setStats({
            totalUsers: 0,
            totalPosts: 0,
            totalEvents: 0,
            totalDonations: 0,
            totalCampaigns: 0,
            activeReports: 0,
            monthlyGrowth: 0
          });
          setMonthlyData([]);
          setCampaignData([]);
          setEventData([]);
          setRecentActivity([]);
          setIsUsingRealData(false);

          const errorMessage = apiError?.response?.data?.message || 'Không thể kết nối đến server';
          console.error(`❌ Dashboard API Error: ${errorMessage}`);
        }


      } catch (error) {
        console.error('❌ Error fetching dashboard data:', error);
        // Set error state or default values
        setStats({
          totalUsers: 0,
          totalPosts: 0,
          totalDonations: 0,
          totalCampaigns: 0,
          totalEvents: 0,
          activeReports: 0,
          monthlyGrowth: 0
        });
        setMonthlyData([]);
        setCampaignData([]);
        setEventData([]);
        setRecentActivity([]);
        setIsUsingRealData(false);
      } finally {
        setLoading(false);
      }
    };

    fetchDashboardData();
  }, []);

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND',
      minimumFractionDigits: 0
    }).format(value);
  };

  const StatCard = ({ title, value, icon: Icon, color, change, prefix = '', suffix = '' }: any) => (
    <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6 hover:shadow-md transition-shadow">
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm font-medium text-gray-600 mb-1">{title}</p>
          <div className="flex items-baseline space-x-2">
            <p className="text-2xl font-bold text-gray-900">
              {prefix}
              <CountUp end={value} duration={2} separator="," />
              {suffix}
            </p>
            {change && (
              <span className={`text-sm font-medium ${change > 0 ? 'text-green-600' : 'text-red-600'}`}>
                {change > 0 ? '+' : ''}{change}%
              </span>
            )}
          </div>
        </div>
        <div className={`p-3 rounded-lg ${color}`}>
          <Icon className="w-6 h-6 text-white" />
        </div>
      </div>
    </div>
  );

  if (loading) {
    return (
      <div className="p-6 space-y-6">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Dashboard Tổng quan</h1>
          <p className="text-gray-600">Đang tải dữ liệu...</p>
        </div>

        {/* Loading skeleton */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {[1, 2, 3, 4].map((i) => (
            <div key={i} className="bg-white rounded-xl shadow-sm border border-gray-100 p-6 animate-pulse">
              <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
              <div className="h-8 bg-gray-200 rounded w-1/2"></div>
            </div>
          ))}
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {[1, 2].map((i) => (
            <div key={i} className="bg-white rounded-xl shadow-sm border border-gray-100 p-6 animate-pulse">
              <div className="h-6 bg-gray-200 rounded w-1/3 mb-6"></div>
              <div className="h-64 bg-gray-200 rounded"></div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 mb-2">Dashboard Tổng quan</h1>
            <p className="text-gray-600">Theo dõi và quản lý hoạt động của nền tảng thiện nguyện</p>
          </div>

          {/* Refresh Button */}
          <button
            onClick={() => window.location.reload()}
            disabled={loading}
            className="flex items-center space-x-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white rounded-lg transition-colors"
          >
            <RefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
            <span>Làm mới</span>
          </button>
        </div>

        {/* Data Source Indicator */}
        <div className={`mt-4 p-3 rounded-lg border ${isUsingRealData
          ? 'bg-green-50 border-green-200 text-green-800'
          : 'bg-red-50 border-red-200 text-red-800'
        }`}>
          <div className="flex items-center space-x-2">
            <div className={`w-2 h-2 rounded-full ${isUsingRealData ? 'bg-green-500' : 'bg-red-500'}`}></div>
            <span className="text-sm font-medium">
              {isUsingRealData
                ? '📊 Dashboard hoạt động với dữ liệu thật từ MongoDB Atlas'
                : '❌ Không thể tải dữ liệu từ API - Vui lòng kiểm tra kết nối server'
              }
            </span>
          </div>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6">
        <StatCard
          title="Tổng người dùng"
          value={stats.totalUsers}
          icon={Users}
          color="bg-blue-500"
          change={stats.monthlyGrowth !== 0 ? stats.monthlyGrowth : null}
        />
        <StatCard
          title="Tổng bài viết"
          value={stats.totalPosts}
          icon={FileText}
          color="bg-green-500"
          change={null}
        />
        <StatCard
          title="Tổng sự kiện"
          value={stats.totalEvents}
          icon={Calendar}
          color="bg-indigo-500"
          change={null}
        />
        <StatCard
          title="Tổng quyên góp"
          value={stats.totalDonations}
          icon={DollarSign}
          color="bg-purple-500"
          change={null}
          suffix=" VNĐ"
        />
        <StatCard
          title="Chiến dịch hoạt động"
          value={stats.totalCampaigns}
          icon={Heart}
          color="bg-pink-500"
          change={null}
        />
      </div>

      {/* Charts Row 1 */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* User Growth Chart */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-semibold text-gray-900">Tăng trưởng người dùng</h3>
            <div className={`flex items-center space-x-2 ${
              stats.monthlyGrowth > 0 ? 'text-green-600' :
              stats.monthlyGrowth < 0 ? 'text-red-600' : 'text-gray-500'
            }`}>
              <TrendingUp className="w-4 h-4" />
              <span className="text-sm font-medium">
                {stats.monthlyGrowth > 0 ? '+' : ''}{stats.monthlyGrowth.toFixed(1)}%
              </span>
            </div>
          </div>
          <ResponsiveContainer width="100%" height={280}>
            <AreaChart data={monthlyData}>
              <defs>
                <linearGradient id="colorUsers" x1="0" y1="0" x2="0" y2="1">
                  <stop offset="5%" stopColor="#3B82F6" stopOpacity={0.8}/>
                  <stop offset="95%" stopColor="#3B82F6" stopOpacity={0.1}/>
                </linearGradient>
              </defs>
              <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
              <XAxis dataKey="month" stroke="#6B7280" fontSize={12} />
              <YAxis stroke="#6B7280" fontSize={12} />
              <Tooltip
                contentStyle={{
                  backgroundColor: 'white',
                  border: '1px solid #e5e7eb',
                  borderRadius: '8px',
                  boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
                }}
              />
              <Area
                type="monotone"
                dataKey="cumulativeUsers"
                stroke="#3B82F6"
                strokeWidth={3}
                fillOpacity={1}
                fill="url(#colorUsers)"
              />
            </AreaChart>
          </ResponsiveContainer>
        </div>

        {/* Campaign Status - Donut Chart */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-6">Trạng thái chiến dịch</h3>
          <div className="flex items-center justify-center">
            <ResponsiveContainer width="100%" height={200}>
              <PieChart>
                <Pie
                  data={campaignData}
                  cx="50%"
                  cy="50%"
                  innerRadius={50}
                  outerRadius={80}
                  paddingAngle={5}
                  dataKey="value"
                >
                  {campaignData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          </div>
          <div className="grid grid-cols-2 gap-2 mt-4">
            {campaignData.map((item, index) => (
              <div key={index} className="flex items-center space-x-2">
                <div
                  className="w-3 h-3 rounded-full"
                  style={{ backgroundColor: item.color }}
                />
                <span className="text-xs text-gray-600">{item.name}: {item.value}</span>
              </div>
            ))}
          </div>
        </div>

        {/* Event Status - Radial Bar Chart */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-6">Trạng thái sự kiện</h3>
          <div className="h-64">
            {eventData.some(item => item.value > 0) ? (
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={eventData.filter(item => item.value > 0)}
                    cx="50%"
                    cy="50%"
                    innerRadius={40}
                    outerRadius={70}
                    paddingAngle={8}
                    dataKey="value"
                  >
                    {eventData.filter(item => item.value > 0).map((entry, index) => {
                      const colors = ['#3B82F6', '#10B981', '#F59E0B', '#EF4444'];
                      return <Cell key={`cell-${index}`} fill={colors[index % colors.length]} />;
                    })}
                  </Pie>
                  <Tooltip />
                </PieChart>
              </ResponsiveContainer>
            ) : (
              <div className="flex flex-col items-center justify-center h-full text-gray-500">
                <Calendar className="w-12 h-12 mb-3 text-gray-300" />
                <p className="text-sm font-medium">Chưa có sự kiện nào</p>
                <p className="text-xs text-gray-400 mt-1">Tạo sự kiện đầu tiên để xem thống kê</p>
              </div>
            )}
          </div>
          <div className="grid grid-cols-2 gap-2 mt-2">
            {eventData.map((item, index) => {
              const colors = ['#3B82F6', '#10B981', '#F59E0B', '#EF4444'];
              return (
                <div key={index} className="flex items-center space-x-2">
                  <div
                    className="w-3 h-3 rounded-full"
                    style={{ backgroundColor: colors[index % colors.length] }}
                  />
                  <span className="text-xs text-gray-600">{item.name}: {item.value}</span>
                </div>
              );
            })}
          </div>
        </div>
      </div>

      {/* Charts Row 2 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Monthly Donations - Enhanced Bar Chart */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-6">Quyên góp theo tháng</h3>
          <ResponsiveContainer width="100%" height={320}>
            <BarChart data={monthlyData} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
              <defs>
                <linearGradient id="colorDonations" x1="0" y1="0" x2="0" y2="1">
                  <stop offset="5%" stopColor="#8B5CF6" stopOpacity={0.8}/>
                  <stop offset="95%" stopColor="#8B5CF6" stopOpacity={0.6}/>
                </linearGradient>
              </defs>
              <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
              <XAxis dataKey="month" stroke="#6B7280" fontSize={12} />
              <YAxis stroke="#6B7280" fontSize={12} tickFormatter={(value) => `${value/1000000}M`} />
              <Tooltip
                formatter={(value: any) => [formatCurrency(value), 'Quyên góp']}
                contentStyle={{
                  backgroundColor: 'white',
                  border: '1px solid #e5e7eb',
                  borderRadius: '8px',
                  boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
                }}
              />
              <Bar
                dataKey="donations"
                fill="url(#colorDonations)"
                radius={[6, 6, 0, 0]}
                stroke="#8B5CF6"
                strokeWidth={1}
              />
            </BarChart>
          </ResponsiveContainer>
        </div>

        {/* Combined Activity Chart - Line + Bar */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-6">Hoạt động tổng hợp</h3>
          <ResponsiveContainer width="100%" height={320}>
            <BarChart data={monthlyData} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
              <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
              <XAxis dataKey="month" stroke="#6B7280" fontSize={12} />
              <YAxis yAxisId="left" stroke="#6B7280" fontSize={12} />
              <YAxis yAxisId="right" orientation="right" stroke="#6B7280" fontSize={12} />
              <Tooltip
                contentStyle={{
                  backgroundColor: 'white',
                  border: '1px solid #e5e7eb',
                  borderRadius: '8px',
                  boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
                }}
              />
              <Bar yAxisId="left" dataKey="users" fill="#3B82F6" radius={[4, 4, 0, 0]} name="Người dùng mới" />
              <Bar yAxisId="right" dataKey="posts" fill="#10B981" radius={[4, 4, 0, 0]} name="Bài viết mới" />
            </BarChart>
          </ResponsiveContainer>
        </div>
      </div>

      {/* Charts Row 3 - Advanced Analytics */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Multi-Line Trend Chart */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-6">Xu hướng hoạt động</h3>
          <ResponsiveContainer width="100%" height={320}>
            <LineChart data={monthlyData} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
              <defs>
                <linearGradient id="colorPosts" x1="0" y1="0" x2="0" y2="1">
                  <stop offset="5%" stopColor="#10B981" stopOpacity={0.3}/>
                  <stop offset="95%" stopColor="#10B981" stopOpacity={0}/>
                </linearGradient>
              </defs>
              <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
              <XAxis dataKey="month" stroke="#6B7280" fontSize={12} />
              <YAxis stroke="#6B7280" fontSize={12} />
              <Tooltip
                contentStyle={{
                  backgroundColor: 'white',
                  border: '1px solid #e5e7eb',
                  borderRadius: '8px',
                  boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
                }}
              />
              <Area
                type="monotone"
                dataKey="posts"
                stroke="#10B981"
                strokeWidth={3}
                fill="url(#colorPosts)"
                dot={{ fill: '#10B981', strokeWidth: 2, r: 5 }}
                activeDot={{ r: 7, stroke: '#10B981', strokeWidth: 2 }}
              />
              <Line
                type="monotone"
                dataKey="users"
                stroke="#3B82F6"
                strokeWidth={3}
                dot={{ fill: '#3B82F6', strokeWidth: 2, r: 5 }}
                activeDot={{ r: 7, stroke: '#3B82F6', strokeWidth: 2 }}
              />
            </LineChart>
          </ResponsiveContainer>
        </div>

        {/* Performance Metrics */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-6">Chỉ số hiệu suất</h3>
          <div className="space-y-6">
            {/* User Engagement - Based on posts per user */}
            <div>
              <div className="flex justify-between items-center mb-2">
                <span className="text-sm font-medium text-gray-700">Tương tác người dùng</span>
                <span className="text-sm font-bold text-blue-600">
                  {stats.totalUsers > 0 ? Math.min(100, Math.round((stats.totalPosts / stats.totalUsers) * 10)) : 0}%
                </span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-3">
                <div
                  className="bg-gradient-to-r from-blue-400 to-blue-600 h-3 rounded-full"
                  style={{width: `${stats.totalUsers > 0 ? Math.min(100, Math.round((stats.totalPosts / stats.totalUsers) * 10)) : 0}%`}}
                ></div>
              </div>
            </div>

            {/* Campaign Success Rate - Based on completed vs total */}
            <div>
              <div className="flex justify-between items-center mb-2">
                <span className="text-sm font-medium text-gray-700">Tỷ lệ thành công chiến dịch</span>
                <span className="text-sm font-bold text-green-600">
                  {stats.totalCampaigns > 0 ? Math.round((campaignData.find(c => c.name === 'Hoàn thành')?.value || 0) / stats.totalCampaigns * 100) : 0}%
                </span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-3">
                <div
                  className="bg-gradient-to-r from-green-400 to-green-600 h-3 rounded-full"
                  style={{width: `${stats.totalCampaigns > 0 ? Math.round((campaignData.find(c => c.name === 'Hoàn thành')?.value || 0) / stats.totalCampaigns * 100) : 0}%`}}
                ></div>
              </div>
            </div>

            {/* Event Participation - Based on events vs users */}
            <div>
              <div className="flex justify-between items-center mb-2">
                <span className="text-sm font-medium text-gray-700">Tham gia sự kiện</span>
                <span className="text-sm font-bold text-purple-600">
                  {stats.totalUsers > 0 ? Math.min(100, Math.round((stats.totalEvents / stats.totalUsers) * 100)) : 0}%
                </span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-3">
                <div
                  className="bg-gradient-to-r from-purple-400 to-purple-600 h-3 rounded-full"
                  style={{width: `${stats.totalUsers > 0 ? Math.min(100, Math.round((stats.totalEvents / stats.totalUsers) * 100)) : 0}%`}}
                ></div>
              </div>
            </div>

            {/* Donation Growth - Based on monthly growth */}
            <div>
              <div className="flex justify-between items-center mb-2">
                <span className="text-sm font-medium text-gray-700">Tăng trưởng người dùng</span>
                <span className="text-sm font-bold text-orange-600">
                  {Math.max(0, Math.min(100, Math.round(stats.monthlyGrowth)))}%
                </span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-3">
                <div
                  className="bg-gradient-to-r from-orange-400 to-orange-600 h-3 rounded-full"
                  style={{width: `${Math.max(0, Math.min(100, Math.round(stats.monthlyGrowth)))}%`}}
                ></div>
              </div>
            </div>

            {/* System Health - Based on active reports */}
            <div>
              <div className="flex justify-between items-center mb-2">
                <span className="text-sm font-medium text-gray-700">Tình trạng hệ thống</span>
                <span className="text-sm font-bold text-emerald-600">
                  {Math.max(0, 100 - (stats.activeReports * 5))}%
                </span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-3">
                <div
                  className="bg-gradient-to-r from-emerald-400 to-emerald-600 h-3 rounded-full"
                  style={{width: `${Math.max(0, 100 - (stats.activeReports * 5))}%`}}
                ></div>
              </div>
            </div>
          </div>
        </div>
      </div>


    </div>
  );
};

export default DashboardOverview;
