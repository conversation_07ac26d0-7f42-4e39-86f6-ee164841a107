import { Request, Response } from 'express';
import { Notification } from '../models/Notification';
import { User } from '../models/user.model';
import { io } from '../index';
import mongoose from 'mongoose';
import { createNotification as createNotificationService, notifyAdminMessage } from '../services/notification.service';

// Create a new notification (legacy function - use service instead)
export const createNotification = async (data: {
  userId: mongoose.Types.ObjectId;
  type: 'donation' | 'campaign' | 'event' | 'post' | 'comment' | 'system' | 'report' | 'admin';
  title: string;
  message: string;
  data?: {
    campaignId?: mongoose.Types.ObjectId;
    donationId?: mongoose.Types.ObjectId;
    eventId?: mongoose.Types.ObjectId;
    postId?: mongoose.Types.ObjectId;
    commentId?: mongoose.Types.ObjectId;
    reportId?: mongoose.Types.ObjectId;
    amount?: number;
    status?: string;
    reason?: string;
    actionType?: 'created' | 'updated' | 'deleted' | 'cancelled' | 'completed' | 'reminder';
    relatedUserId?: mongoose.Types.ObjectId;
    relatedUserName?: string;
  };
}) => {
  try {
    const notification = new Notification(data);
    await notification.save();

    // Emit socket event for real-time notification
    io.to(`user-${data.userId}`).emit('new_notification', notification);

    return notification;
  } catch (error) {
    console.error('Error creating notification:', error);
    throw error;
  }
};

// Get user's notifications
export const getUserNotifications = async (req: Request, res: Response) => {
  try {
    const userId = (req as any).user?._id;
    if (!userId) {
      return res.status(401).json({
        success: false,
        message: 'Unauthorized'
      });
    }

    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;
    const skip = (page - 1) * limit;

    const [notifications, total] = await Promise.all([
      Notification.find({ userId })
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limit),
      Notification.countDocuments({ userId })
    ]);

    return res.json({
      success: true,
      data: notifications,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Error getting user notifications:', error);
    return res.status(500).json({
      success: false,
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

// Mark notification as read
export const markAsRead = async (req: Request, res: Response) => {
  try {
    const userId = (req as any).user?._id;
    if (!userId) {
      return res.status(401).json({
        success: false,
        message: 'Unauthorized'
      });
    }

    const { notificationId } = req.params;
    const notification = await Notification.findOneAndUpdate(
      { _id: notificationId, userId },
      { isRead: true },
      { new: true }
    );

    if (!notification) {
      return res.status(404).json({
        success: false,
        message: 'Notification not found'
      });
    }

    return res.json({
      success: true,
      data: notification
    });
  } catch (error) {
    console.error('Error marking notification as read:', error);
    return res.status(500).json({
      success: false,
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

// Mark all notifications as read
export const markAllAsRead = async (req: Request, res: Response) => {
  try {
    const userId = (req as any).user?._id;
    if (!userId) {
      return res.status(401).json({
        success: false,
        message: 'Unauthorized'
      });
    }

    await Notification.updateMany(
      { userId, isRead: false },
      { isRead: true }
    );

    return res.json({
      success: true,
      message: 'All notifications marked as read'
    });
  } catch (error) {
    console.error('Error marking all notifications as read:', error);
    return res.status(500).json({
      success: false,
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

// Get unread notifications count
export const getUnreadCount = async (req: Request, res: Response) => {
  try {
    const userId = (req as any).user?._id;
    if (!userId) {
      return res.status(401).json({
        success: false,
        message: 'Unauthorized'
      });
    }

    const count = await Notification.countDocuments({
      userId,
      isRead: false
    });

    return res.json({
      success: true,
      count
    });
  } catch (error) {
    console.error('Error getting unread notifications count:', error);
    return res.status(500).json({
      success: false,
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

// Admin: Create notification for users
export const createAdminNotification = async (req: Request, res: Response) => {
  try {
    const user = (req as any).user;
    if (!user || user.role !== 'admin') {
      return res.status(403).json({
        success: false,
        message: 'Access denied. Admin only.'
      });
    }

    const { title, message, userIds, sendToAll } = req.body;

    if (!title || !message) {
      return res.status(400).json({
        success: false,
        message: 'Title and message are required'
      });
    }

    let targetUserIds: mongoose.Types.ObjectId[] = [];

    if (sendToAll) {
      // Send to all users
      const allUsers = await User.find({ role: 'user' }).select('_id');
      targetUserIds = allUsers.map(u => u._id);
    } else if (userIds && Array.isArray(userIds)) {
      // Send to specific users
      targetUserIds = userIds.map((id: string) => new mongoose.Types.ObjectId(id));
    } else {
      return res.status(400).json({
        success: false,
        message: 'Either userIds array or sendToAll flag is required'
      });
    }

    if (targetUserIds.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'No users found to send notification'
      });
    }

    // Create notifications using service
    await notifyAdminMessage(targetUserIds, title, message);

    return res.json({
      success: true,
      message: `Notification sent to ${targetUserIds.length} users`,
      count: targetUserIds.length
    });
  } catch (error) {
    console.error('Error creating admin notification:', error);
    return res.status(500).json({
      success: false,
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

// Admin: Get notification statistics
export const getNotificationStats = async (req: Request, res: Response) => {
  try {
    const user = (req as any).user;
    if (!user || user.role !== 'admin') {
      return res.status(403).json({
        success: false,
        message: 'Access denied. Admin only.'
      });
    }

    const [totalNotifications, unreadNotifications, notificationsByType] = await Promise.all([
      Notification.countDocuments(),
      Notification.countDocuments({ isRead: false }),
      Notification.aggregate([
        {
          $group: {
            _id: '$type',
            count: { $sum: 1 }
          }
        }
      ])
    ]);

    return res.json({
      success: true,
      data: {
        total: totalNotifications,
        unread: unreadNotifications,
        byType: notificationsByType
      }
    });
  } catch (error) {
    console.error('Error getting notification stats:', error);
    return res.status(500).json({
      success: false,
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

// Test notification endpoint (for development)
export const createTestNotification = async (req: Request, res: Response) => {
  try {
    const user = (req as any).user;
    if (!user) {
      return res.status(401).json({
        success: false,
        message: 'Unauthorized'
      });
    }

    const { type = 'system', title = 'Test Notification', message = 'This is a test notification' } = req.body;

    await createNotificationService({
      userId: user._id,
      type,
      title,
      message,
      data: {
        actionType: 'created'
      },
      priority: 'medium',
      sendEmail: false
    });

    return res.json({
      success: true,
      message: 'Test notification created successfully'
    });
  } catch (error) {
    console.error('Error creating test notification:', error);
    return res.status(500).json({
      success: false,
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};