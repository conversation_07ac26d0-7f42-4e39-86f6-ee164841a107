import crypto from 'crypto';
import axios from 'axios';
import { Donation } from '../models/Donation';
import { Campaign } from '../models/Campaign';
import { config } from '../config';
import { io } from '../index';
import mongoose from 'mongoose';
import { IDonation } from '../models/Donation';

interface MomoPaymentData {
  partnerCode: string;
  requestId: string;
  amount: number;
  orderId: string;
  orderInfo: string;
  redirectUrl: string;
  ipnUrl: string;
  requestType: string;
  extraData: string;
  signature?: string;
}

interface MomoCallbackData {
  partnerCode: string;
  orderId: string;
  requestId: string;
  amount: number;
  orderInfo: string;
  orderType: string;
  transId: string;
  resultCode: number;
  message: string;
  payType: string;
  signature: string;
}

interface MomoRequestData {
  partnerCode: string;
  requestId: string;
  amount: string;
  orderId: string;
  orderInfo: string;
  redirectUrl: string;
  ipnUrl: string;
  extraData: string;
  requestType: string;
  accessKey: string;
  signature?: string;
}

export class MomoService {
  private apiUrl: string;
  private partnerCode: string;
  private accessKey: string;
  private secretKey: string;
  private ipnUrl: string;
  private redirectUrl: string;

  constructor() {
    this.apiUrl = 'https://test-payment.momo.vn/v2/gateway/api/create';
    this.partnerCode = config.momo.partnerCode;
    this.accessKey = config.momo.accessKey;
    this.secretKey = config.momo.secretKey;
    this.ipnUrl = config.momo.ipnUrl;
    this.redirectUrl = config.momo.returnUrl;

    // Log configuration
    console.log('MoMo Service Configuration:', {
      apiUrl: this.apiUrl,
      partnerCode: this.partnerCode,
      accessKey: this.accessKey ? '***' : undefined,
      secretKey: this.secretKey ? '***' : undefined,
      ipnUrl: this.ipnUrl,
      redirectUrl: this.redirectUrl
    });
  }

  private generateSignature(data: Record<string, any>): string {
    try {
      // Create a copy of the data without signature
      const dataToSign = { ...data };
      delete dataToSign.signature;

      // Validate required fields
      const requiredFields = [
        'amount', 'orderId', 'orderInfo', 'partnerCode',
        'redirectUrl', 'ipnUrl', 'requestId', 'requestType'
      ];

      for (const field of requiredFields) {
        if (!dataToSign[field]) {
          throw new Error(`Missing required field: ${field}`);
        }
      }

      // Create raw signature string with exact order of parameters
      const rawSignature = [
        `accessKey=${this.accessKey}`,
        `amount=${dataToSign.amount}`,
        `extraData=${dataToSign.extraData || ''}`,
        `ipnUrl=${dataToSign.ipnUrl}`,
        `orderId=${dataToSign.orderId}`,
        `orderInfo=${dataToSign.orderInfo}`,
        `partnerCode=${dataToSign.partnerCode}`,
        `redirectUrl=${dataToSign.redirectUrl}`,
        `requestId=${dataToSign.requestId}`,
        `requestType=${dataToSign.requestType}`
      ].join('&');

      console.log('Raw signature string:', rawSignature);

      // Create HMAC SHA256 signature
      const signature = crypto
        .createHmac('sha256', this.secretKey)
        .update(rawSignature)
        .digest('hex');

      console.log('Generated signature:', signature);

      return signature;
    } catch (error) {
      console.error('Error generating signature:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      throw new Error(`Failed to generate signature: ${errorMessage}`);
    }
  }

  async createPaymentUrl(donation: IDonation): Promise<string> {
    try {
      // Validate donation data
      if (!donation.transactionId) {
        throw new Error('Transaction ID is required');
      }
      if (!donation.amount || donation.amount <= 0) {
        throw new Error('Invalid donation amount');
      }
      if (!donation.campaignId) {
        throw new Error('Campaign ID is required');
      }

      // Format amount to ensure it's a valid integer string
      const amount = Math.round(donation.amount).toString();
      if (isNaN(parseInt(amount))) {
        throw new Error('Invalid amount format');
      }

      const orderId = donation.transactionId;
      const requestId = Date.now().toString();
      const orderInfo = `Donation for campaign: ${donation.campaignId}`;

      // Validate URLs
      if (!this.redirectUrl || !this.ipnUrl) {
        throw new Error('Missing required URLs configuration');
      }

      // Create request data without signature first
      const requestData: MomoRequestData = {
        partnerCode: this.partnerCode,
        requestId: requestId,
        amount: amount,
        orderId: orderId,
        orderInfo: orderInfo,
        redirectUrl: this.redirectUrl,
        ipnUrl: this.ipnUrl,
        extraData: '', // Empty string for extraData
        requestType: 'captureWallet',
        accessKey: this.accessKey
      };

      // Validate request data
      if (!this.partnerCode || !this.accessKey || !this.secretKey) {
        throw new Error('Missing MoMo configuration');
      }

      // Log raw request data before signature
      console.log('Raw request data before signature:', {
        ...requestData,
        accessKey: '***'
      });

      // Generate signature
      const signature = this.generateSignature(requestData);
      requestData.signature = signature;

      // Log final request data
      console.log('Final MoMo request data:', {
        ...requestData,
        accessKey: '*****'
      });

      console.log('Calling MoMo API:', this.apiUrl);

      // Make API request with timeout
      const response = await axios.post(this.apiUrl, requestData, {
        headers: {
          'Content-Type': 'application/json'
        },
        timeout: 10000 // 10 seconds timeout
      });

      console.log('MoMo API response:', response.data);

      if (response.data.resultCode === 0) {
        return response.data.payUrl;
      } else {
        throw new Error(`MoMo error: ${response.data.message} (Code: ${response.data.resultCode})`);
      }
    } catch (error: any) {
      console.error('Error creating MoMo payment URL:', error.response?.data || error);

      // Handle specific error cases
      if (error.code === 'ECONNABORTED') {
        throw new Error('Request timeout - MoMo service is not responding');
      }

      if (error.response?.data?.resultCode === 11007) {
        throw new Error('Invalid signature - Please check MoMo configuration');
      }

      throw new Error(error.response?.data?.message || error.message || 'Unknown error occurred');
    }
  }

  async verifyCallback(data: any): Promise<boolean> {
    try {
      const signature = data.signature;
      const dataToVerify = { ...data };
      delete dataToVerify.signature;

      const calculatedSignature = this.generateSignature(dataToVerify);
      console.log('Verifying callback signature:', {
        received: signature,
        calculated: calculatedSignature
      });

      return signature === calculatedSignature;
    } catch (error) {
      console.error('Error verifying MoMo callback:', error);
      return false;
    }
  }

    public async handlePaymentCallback(data: MomoCallbackData) {
    const session = await mongoose.startSession();
    try {
      await session.withTransaction(async () => {
        // Verify signature
        if (!await this.verifyCallback(data)) {
          throw new Error('Invalid signature');
        }

        const donation = await Donation.findOne({ transactionId: data.orderId }).session(session);
        if (!donation) {
          throw new Error('Donation not found');
        }

        if (donation.status !== 'pending') {
          console.log(`Donation ${data.orderId} already processed (${donation.status})`);
          return;
        }

        if (Number(data.amount) !== donation.amount) {
          throw new Error(`Amount mismatch: received ${data.amount}, expected ${donation.amount}`);
        }

        if (data.resultCode === 0) {
          // Payment successful
          donation.status = 'success';
          donation.paymentDetails = {
            momoTransactionId: data.transId,
            momoResponseTime: Date.now(),
            momoPayType: data.payType
          };
          await donation.save({ session });

          // Update campaign using updateOne to avoid validation issues
          const campaign = await Campaign.findById(donation.campaignId).session(session);
          if (campaign) {
            const oldAmount = campaign.currentAmount || 0;
            const newAmount = oldAmount + donation.amount;
            const newTotalDonations = (campaign.totalDonations || 0) + 1;

            // Mỗi lần thanh toán thành công sẽ tăng 1 lượt ủng hộ (không kiểm tra email trùng)
            const newTotalDonors = (campaign.totalDonors || 0) + 1;

            // Create donor object
            const newDonor = {
              transactionId: donation.transactionId,
              amount: donation.amount,
              name: donation.isAnonymous ? 'Người ủng hộ ẩn danh' : donation.name || 'Anonymous',
              email: donation.email || '',
              phone: donation.phone || '',
              address: donation.address || '',
              isAnonymous: donation.isAnonymous || false,
              paymentMethod: donation.paymentMethod,
              status: 'success',
              createdAt: new Date()
            };

            // Recalculate progress
            const newProgress = Math.min(100, (newAmount / campaign.targetAmount) * 100);

            console.log('[MoMo IPN] Campaign update details:', {
              oldAmount,
              newAmount,
              oldProgress: (oldAmount / campaign.targetAmount) * 100,
              newProgress,
              totalDonations: newTotalDonations,
              totalDonors: newTotalDonors
            });

            // Update campaign using updateOne to avoid validation issues
            await Campaign.updateOne(
              { _id: donation.campaignId },
              {
                $inc: {
                  currentAmount: donation.amount,
                  totalDonations: 1,
                  totalDonors: 1
                },
                $push: {
                  donors: newDonor
                },
                $set: {
                  progress: newProgress
                }
              },
              {
                session,
                runValidators: false // Skip validation for partial updates
              }
            );

            // Get updated campaign data for socket event
            const updatedCampaign = await Campaign.findById(donation.campaignId).session(session);

            console.log('[MoMo IPN] Campaign updated successfully:', {
              campaignId: updatedCampaign?._id,
              currentAmount: updatedCampaign?.currentAmount,
              totalDonations: updatedCampaign?.totalDonations,
              totalDonors: updatedCampaign?.totalDonors,
              progress: updatedCampaign?.progress
            });

            // Emit socket event for real-time updates
            if (updatedCampaign) {
              io.emit('campaign_updated', {
                campaignId: (updatedCampaign._id as mongoose.Types.ObjectId).toString(),
                currentAmount: updatedCampaign.currentAmount,
                totalDonations: updatedCampaign.totalDonations,
                totalDonors: updatedCampaign.totalDonors,
                progress: updatedCampaign.progress,
                lastDonation: {
                  amount: donation.amount,
                  donorName: donation.isAnonymous ? 'Người ủng hộ ẩn danh' : donation.name || 'Anonymous',
                  paymentMethod: donation.paymentMethod,
                  createdAt: new Date(),
                  transactionId: donation.transactionId
                }
              });
            }
          }
        } else {
          donation.status = 'failed';
          donation.paymentDetails = {
            momoTransactionId: data.transId,
            momoResponseTime: Date.now(),
            momoPayType: data.payType,
            momoErrorCode: data.resultCode,
            momoErrorMessage: data.message
          };
          await donation.save({ session });
        }
      });

      return {
        success: true,
        message: 'Payment processed successfully'
      };
    } catch (error) {
      console.error('Error processing MoMo payment:', error);
      return {
        success: false,
        message: error instanceof Error ? error.message : 'Unknown error'
      };
    } finally {
      session.endSession();
    }
  }
}