<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>C<PERSON><PERSON> ơn bạn đã quyên góp</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f4f4f4;
        }
        .container {
            background-color: #ffffff;
            padding: 40px;
            border-radius: 15px;
            box-shadow: 0 0 30px rgba(0,0,0,0.1);
            border-top: 5px solid #e74c3c;
        }
        .header {
            text-align: center;
            margin-bottom: 40px;
        }
        .logo {
            font-size: 32px;
            font-weight: bold;
            color: #e74c3c;
            margin-bottom: 10px;
        }
        .thank-you-badge {
            display: inline-block;
            background: linear-gradient(135deg, #e74c3c, #c0392b);
            color: white;
            padding: 10px 25px;
            border-radius: 25px;
            font-size: 14px;
            font-weight: bold;
            text-transform: uppercase;
            letter-spacing: 1px;
            margin-bottom: 20px;
        }
        .greeting {
            font-size: 24px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 20px;
            text-align: center;
        }
        .message {
            font-size: 16px;
            line-height: 1.8;
            color: #34495e;
            margin-bottom: 30px;
            text-align: center;
        }
        .donation-details {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            border-radius: 10px;
            padding: 25px;
            margin: 30px 0;
            border-left: 5px solid #e74c3c;
        }
        .detail-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #dee2e6;
        }
        .detail-row:last-child {
            border-bottom: none;
        }
        .detail-label {
            font-weight: 600;
            color: #495057;
        }
        .detail-value {
            font-weight: bold;
            color: #2c3e50;
        }
        .amount {
            font-size: 24px;
            color: #e74c3c;
            font-weight: bold;
        }
        .impact-section {
            background: linear-gradient(135deg, #e8f5e8, #d4edda);
            border-radius: 10px;
            padding: 25px;
            margin: 30px 0;
            text-align: center;
            border-left: 5px solid #28a745;
        }
        .impact-title {
            font-size: 18px;
            font-weight: bold;
            color: #155724;
            margin-bottom: 15px;
        }
        .impact-text {
            color: #155724;
            font-size: 14px;
            line-height: 1.6;
        }
        .next-steps {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 10px;
            padding: 20px;
            margin: 30px 0;
        }
        .next-steps h3 {
            color: #856404;
            margin-bottom: 15px;
            font-size: 16px;
        }
        .next-steps ul {
            color: #856404;
            margin: 0;
            padding-left: 20px;
        }
        .next-steps li {
            margin-bottom: 8px;
        }
        .social-section {
            text-align: center;
            margin: 30px 0;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
        }
        .social-title {
            font-size: 16px;
            font-weight: bold;
            color: #495057;
            margin-bottom: 15px;
        }
        .social-buttons {
            display: flex;
            justify-content: center;
            gap: 15px;
            flex-wrap: wrap;
        }
        .social-button {
            display: inline-block;
            padding: 10px 20px;
            border-radius: 25px;
            text-decoration: none;
            font-weight: bold;
            font-size: 12px;
            transition: transform 0.2s;
        }
        .social-button:hover {
            transform: translateY(-2px);
        }
        .facebook { background: #3b5998; color: white; }
        .twitter { background: #1da1f2; color: white; }
        .linkedin { background: #0077b5; color: white; }
        .footer {
            text-align: center;
            margin-top: 40px;
            padding-top: 30px;
            border-top: 2px solid #ecf0f1;
            color: #7f8c8d;
            font-size: 14px;
        }
        .contact-info {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            text-align: center;
        }
        .contact-info h4 {
            color: #495057;
            margin-bottom: 10px;
        }
        .contact-info p {
            margin: 5px 0;
            color: #6c757d;
        }
        .heart {
            color: #e74c3c;
            font-size: 20px;
            animation: heartbeat 1.5s ease-in-out infinite;
        }
        @keyframes heartbeat {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }
        .anonymous-note {
            background: #e3f2fd;
            border: 1px solid #bbdefb;
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
            text-align: center;
            color: #1565c0;
            font-style: italic;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">KeyDyWeb</div>
            <div class="thank-you-badge">Cảm ơn bạn</div>
        </div>
        
        <div class="greeting">
            Xin chào {{donorName}}! <span class="heart">❤️</span>
        </div>
        
        <div class="message">
            Chúng tôi vô cùng biết ơn và xúc động trước tấm lòng hảo tâm của bạn. 
            Sự đóng góp của bạn không chỉ là một khoản tiền, mà còn là niềm hy vọng 
            và tình yêu thương mà bạn gửi gắm đến những người cần được giúp đỡ.
        </div>

        {{#if isAnonymous}}
        <div class="anonymous-note">
            <strong>🎭 Quyên góp ẩn danh</strong><br>
            Bạn đã chọn quyên góp ẩn danh. Danh tính của bạn sẽ được bảo mật và hiển thị là "Người ủng hộ ẩn danh" trên website.
        </div>
        {{/if}}

        <div class="donation-details">
            <h3 style="margin-top: 0; color: #2c3e50; text-align: center;">Chi tiết quyên góp</h3>
            
            <div class="detail-row">
                <span class="detail-label">💰 Số tiền quyên góp:</span>
                <span class="detail-value amount">{{amount}}</span>
            </div>
            
            <div class="detail-row">
                <span class="detail-label">🎯 Chiến dịch:</span>
                <span class="detail-value">{{campaignTitle}}</span>
            </div>
            
            <div class="detail-row">
                <span class="detail-label">🔢 Mã giao dịch:</span>
                <span class="detail-value">{{transactionId}}</span>
            </div>
            
            <div class="detail-row">
                <span class="detail-label">📅 Ngày quyên góp:</span>
                <span class="detail-value">{{donationDate}}</span>
            </div>
        </div>

        <div class="impact-section">
            <div class="impact-title">🌟 Tác động của bạn</div>
            <div class="impact-text">
                Với khoản quyên góp <strong>{{amount}}</strong> này, bạn đã góp phần tạo nên những thay đổi tích cực 
                trong cộng đồng. Mỗi đồng tiền đều được sử dụng một cách minh bạch và hiệu quả nhất 
                để mang lại hy vọng cho những hoàn cảnh khó khăn.
            </div>
        </div>

        <div class="next-steps">
            <h3>📋 Những điều bạn có thể làm tiếp theo:</h3>
            <ul>
                <li>Theo dõi tiến độ chiến dịch trên website KeyDyWeb</li>
                <li>Chia sẻ chiến dịch với bạn bè để lan tỏa tình yêu thương</li>
                <li>Đăng ký nhận thông báo về các chiến dịch mới</li>
                <li>Tham gia cộng đồng tình nguyện viên của chúng tôi</li>
            </ul>
        </div>

        <div class="social-section">
            <div class="social-title">📢 Chia sẻ tình yêu thương</div>
            <div class="social-buttons">
                <a href="#" class="social-button facebook">Chia sẻ Facebook</a>
                <a href="#" class="social-button twitter">Chia sẻ Twitter</a>
                <a href="#" class="social-button linkedin">Chia sẻ LinkedIn</a>
            </div>
        </div>

        <div class="contact-info">
            <h4>📞 Liên hệ với chúng tôi</h4>
            <p><strong>Email:</strong> <EMAIL></p>
            <p><strong>Hotline:</strong> 1900 1234</p>
            <p><strong>Website:</strong> www.keydyweb.com</p>
        </div>
        
        <div class="footer">
            <p><strong>Một lần nữa, xin chân thành cảm ơn bạn!</strong></p>
            <p>Sự hỗ trợ của bạn là động lực để chúng tôi tiếp tục sứ mệnh kết nối những tấm lòng hảo tâm.</p>
            <p style="margin-top: 20px;">
                <span class="heart">❤️</span> Với tình cảm và lòng biết ơn sâu sắc <span class="heart">❤️</span>
            </p>
            <p><strong>Đội ngũ KeyDyWeb</strong></p>
            <p style="margin-top: 30px; font-size: 12px; color: #95a5a6;">
                &copy; 2024 KeyDyWeb. Tất cả quyền được bảo lưu.<br>
                Email này được gửi tự động, vui lòng không trả lời trực tiếp.
            </p>
        </div>
    </div>
</body>
</html>
