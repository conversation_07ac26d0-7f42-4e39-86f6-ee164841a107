"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const campaignController_1 = require("../controllers/campaignController");
const upload_1 = __importDefault(require("../middleware/upload"));
const authMiddleware_1 = require("../middleware/authMiddleware");
const router = express_1.default.Router();
console.log('🔧 [Campaign] Campaign routes loaded');
// Test route
router.get('/admin/test', authMiddleware_1.protect, authMiddleware_1.admin, (req, res) => {
    var _a;
    console.log('🧪 [Campaign] Test route called');
    res.json({ success: true, message: 'Campaign admin test route working', user: (_a = req.user) === null || _a === void 0 ? void 0 : _a.name });
});
// Admin routes
router.post('/admin/simple', authMiddleware_1.protect, authMiddleware_1.admin, campaignController_1.createCampaign); // Simple route without file upload
router.post('/admin', authMiddleware_1.protect, authMiddleware_1.admin, upload_1.default.array('images', 5), campaignController_1.createCampaign);
router.get('/admin', authMiddleware_1.protect, authMiddleware_1.admin, campaignController_1.getCampaigns);
router.put('/admin/:id', authMiddleware_1.protect, authMiddleware_1.admin, upload_1.default.array('images', 5), campaignController_1.updateCampaign);
router.delete('/admin/:id', authMiddleware_1.protect, authMiddleware_1.admin, campaignController_1.deleteCampaign);
router.patch('/admin/:id/status', authMiddleware_1.protect, authMiddleware_1.admin, campaignController_1.updateCampaignStatus);
router.post('/admin/update-statuses', authMiddleware_1.protect, authMiddleware_1.admin, campaignController_1.manualUpdateCampaignStatuses);
// Public routes
router.get('/', campaignController_1.getPublicCampaigns);
router.get('/:id', campaignController_1.getCampaignById);
exports.default = router;
