import express from 'express';
import { Campaign } from '../models/Campaign';

const router = express.Router();

// Temporary endpoint to add categories to existing campaigns
router.post('/add-categories', async (req, res) => {
  try {
    console.log('🔄 Bắt đầu thêm danh mục cho các chiến dịch...');
    
    const categories = [
      'Giáo dục',
      'Y tế', 
      'Trẻ em',
      'Người già',
      'Môi trường',
      'Thiên tai',
      'Động vật',
      'Cộng đồng',
      '<PERSON><PERSON><PERSON> hóa',
      'Th<PERSON> thao'
    ];
    
    // L<PERSON>y tất cả campaigns
    const campaigns = await Campaign.find({});
    console.log(`📊 Tìm thấy ${campaigns.length} chiến dịch`);
    
    if (campaigns.length === 0) {
      return res.json({ 
        success: false, 
        message: 'Không có chiến dịch nào để cập nhật' 
      });
    }
    
    let updatedCount = 0;
    
    // C<PERSON>p nhật từng campaign với category ngẫu nhiên
    for (let i = 0; i < campaigns.length; i++) {
      const campaign = campaigns[i];
      
      // Chỉ thêm category nếu chưa có hoặc category là 'Khác'
      if (!campaign.category || campaign.category === 'Khác') {
        const randomCategory = categories[Math.floor(Math.random() * categories.length)];
        
        await Campaign.findByIdAndUpdate(campaign._id, {
          category: randomCategory
        });
        
        console.log(`✅ Cập nhật "${campaign.title}" với danh mục: ${randomCategory}`);
        updatedCount++;
      } else {
        console.log(`⏭️  "${campaign.title}" đã có danh mục: ${campaign.category}`);
      }
    }
    
    console.log(`🎉 Hoàn thành! Đã cập nhật ${updatedCount} chiến dịch`);
    
    // Hiển thị thống kê
    const updatedCampaigns = await Campaign.find({});
    const categoryStats: { [key: string]: number } = {};
    
    updatedCampaigns.forEach(campaign => {
      if (campaign.category) {
        categoryStats[campaign.category] = (categoryStats[campaign.category] || 0) + 1;
      }
    });
    
    console.log('\n📈 Thống kê danh mục:');
    Object.entries(categoryStats).forEach(([category, count]) => {
      console.log(`   ${category}: ${count} chiến dịch`);
    });
    
    res.json({
      success: true,
      message: `Đã cập nhật ${updatedCount} chiến dịch với danh mục`,
      stats: categoryStats,
      totalCampaigns: campaigns.length,
      updatedCampaigns: updatedCount
    });
    
  } catch (error) {
    console.error('❌ Lỗi khi thêm danh mục:', error);
    res.status(500).json({
      success: false,
      message: 'Lỗi khi thêm danh mục',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Get category statistics
router.get('/category-stats', async (req, res) => {
  try {
    const campaigns = await Campaign.find({});
    const categoryStats: { [key: string]: number } = {};
    
    campaigns.forEach(campaign => {
      if (campaign.category) {
        categoryStats[campaign.category] = (categoryStats[campaign.category] || 0) + 1;
      }
    });
    
    res.json({
      success: true,
      stats: categoryStats,
      totalCampaigns: campaigns.length
    });
    
  } catch (error) {
    console.error('❌ Lỗi khi lấy thống kê danh mục:', error);
    res.status(500).json({
      success: false,
      message: 'Lỗi khi lấy thống kê danh mục',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

export default router;
