import { useState, useEffect } from 'react';
import axios from 'axios';
import { useAuth } from '../contexts/AuthContext';
import { io, Socket } from 'socket.io-client';

const API_URL = import.meta.env.VITE_API_URL || '';

interface Notification {
  _id: string;
  type: 'donation' | 'campaign' | 'event' | 'post' | 'comment' | 'system' | 'report' | 'admin';
  title: string;
  message: string;
  data?: {
    campaignId?: string;
    donationId?: string;
    eventId?: string;
    postId?: string;
    commentId?: string;
    reportId?: string;
    amount?: number;
    status?: string;
    reason?: string;
    actionType?: 'created' | 'updated' | 'deleted' | 'cancelled' | 'completed' | 'reminder';
    relatedUserId?: string;
    relatedUserName?: string;
  };
  isRead: boolean;
  emailSent: boolean;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  createdAt: string;
  updatedAt: string;
}

export const useNotifications = () => {
  const { user } = useAuth();
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [unreadCount, setUnreadCount] = useState(0);
  const [loading, setLoading] = useState(false);
  const [socket, setSocket] = useState<Socket | null>(null);

  // Fetch notifications
  const fetchNotifications = async () => {
    if (!user) return;

    try {
      setLoading(true);
      const token = localStorage.getItem('token');
      const response = await axios.get(`${API_URL}/api/notifications`, {
        headers: { Authorization: `Bearer ${token}` }
      });

      console.log('📧 Notifications response:', response.data);

      if (response.data.success) {
        const notifications = response.data.data || [];
        console.log('📧 Notifications data:', notifications);
        setNotifications(notifications);
        const unread = notifications.filter((n: Notification) => !n.isRead).length;
        setUnreadCount(unread);
      } else {
        console.warn('📧 Notifications request failed:', response.data);
        setNotifications([]);
        setUnreadCount(0);
      }
    } catch (error) {
      console.error('📧 Error fetching notifications:', error);
      setNotifications([]);
      setUnreadCount(0);
    } finally {
      setLoading(false);
    }
  };

  // Fetch unread count only
  const fetchUnreadCount = async () => {
    if (!user) return;

    try {
      const token = localStorage.getItem('token');
      const response = await axios.get(`${API_URL}/api/notifications/unread-count`, {
        headers: { Authorization: `Bearer ${token}` }
      });

      console.log('🔔 Unread count response:', response.data);

      if (response.data.success) {
        setUnreadCount(response.data.count || 0);
      } else {
        console.warn('🔔 Unread count request failed:', response.data);
        setUnreadCount(0);
      }
    } catch (error) {
      console.error('🔔 Error fetching unread count:', error);
      setUnreadCount(0);
    }
  };

  // Mark notification as read
  const markAsRead = async (notificationId: string) => {
    if (!user) return;

    try {
      const token = localStorage.getItem('token');
      await axios.patch(`${API_URL}/api/notifications/${notificationId}/read`, {}, {
        headers: { Authorization: `Bearer ${token}` }
      });

      // Update local state
      setNotifications(prev =>
        prev.map(n =>
          n._id === notificationId ? { ...n, isRead: true } : n
        )
      );
      setUnreadCount(prev => Math.max(0, prev - 1));
    } catch (error) {
      console.error('Error marking notification as read:', error);
    }
  };

  // Mark all notifications as read
  const markAllAsRead = async () => {
    if (!user) return;

    try {
      const token = localStorage.getItem('token');
      await axios.patch(`${API_URL}/api/notifications/read-all`, {}, {
        headers: { Authorization: `Bearer ${token}` }
      });

      // Update local state
      setNotifications(prev =>
        prev.map(n => ({ ...n, isRead: true }))
      );
      setUnreadCount(0);
    } catch (error) {
      console.error('Error marking all notifications as read:', error);
    }
  };

  // Socket.IO connection and real-time notifications
  useEffect(() => {
    if (user) {
      // Connect to socket
      const newSocket = io(API_URL || 'http://localhost:5001', {
        transports: ['websocket', 'polling']
      });

      // Join user room for personal notifications
      newSocket.emit('join-user', user._id);

      // Listen for new notifications
      newSocket.on('new_notification', (notification: Notification) => {
        console.log('📧 Real-time notification received:', notification);

        // Add to notifications list
        setNotifications(prev => [notification, ...prev]);

        // Update unread count
        if (!notification.isRead) {
          setUnreadCount(prev => prev + 1);
        }

        // Show browser notification if permission granted
        if (Notification.permission === 'granted') {
          new Notification(notification.title, {
            body: notification.message,
            icon: '/favicon.ico'
          });
        }
      });

      setSocket(newSocket);

      // Initial fetch
      fetchNotifications();

      return () => {
        newSocket.disconnect();
        setSocket(null);
      };
    } else {
      setNotifications([]);
      setUnreadCount(0);
      if (socket) {
        socket.disconnect();
        setSocket(null);
      }
    }
  }, [user]);

  // Request notification permission
  useEffect(() => {
    if ('Notification' in window && Notification.permission === 'default') {
      Notification.requestPermission();
    }
  }, []);

  // Periodic check for new notifications (every 30 seconds)
  useEffect(() => {
    if (!user) return;

    const interval = setInterval(() => {
      fetchUnreadCount();
    }, 30000); // 30 seconds

    return () => clearInterval(interval);
  }, [user]);

  return {
    notifications,
    unreadCount,
    loading,
    hasNewNotifications: unreadCount > 0,
    fetchNotifications,
    fetchUnreadCount,
    markAsRead,
    markAllAsRead,
    refetch: fetchNotifications
  };
};
