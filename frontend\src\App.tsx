import { BrowserRouter, Routes, Route } from 'react-router-dom';
import Layout from './components/layout/layout';
import { AuthProvider } from './contexts/AuthContext';
import { PostsProvider } from './contexts/PostsContext';
import { ThemeProvider } from './components/layout/theme-provider';
import HomePage from './pages/HomePage';
import Events from './pages/Events';
import EventDetail from './pages/EventDetail';
import MyEvents from './pages/MyEvents';
import Campaigns from './pages/Campaigns';
import ProfilePage from './pages/ProfilePage';
import Dashboard from './pages/Dashboard';
import LoginPage from './pages/LoginPage';
import RegisterPage from './pages/RegisterPage';
import NotFound from './pages/NotFound';
import CampaignDetail from './pages/CampaignDetail';
import DonationSuccess from './pages/DonationSuccess';
import MomoReturnPage from './pages/payment/MomoReturnPage';
import PayOSReturnPage from './pages/payment/PayOSReturnPage';
import PostDetail from './pages/PostDetail';
import Posts from './pages/Posts';
import AdminDashboard from './pages/admin/AdminDashboard';
import ManagePosts from './pages/admin/ManagePosts';
import ManageCampaignsNew from './pages/admin/ManageCampaignsNew';
import ManageDonations from './pages/admin/ManageDonations';
import ManageUsers from './pages/admin/ManageUsers';
import ManageEvents from './pages/admin/ManageEvents';
import ManageCommentReports from './pages/admin/ManageCommentReports';
import DashboardOverview from './pages/admin/DashboardOverview';
import AdminLogin from './pages/admin/AdminLogin';
import NotificationManagement from './components/admin/NotificationManagement';

function App() {
  return (
    <ThemeProvider>
      <AuthProvider>
        <PostsProvider>
          <BrowserRouter future={{ v7_startTransition: true, v7_relativeSplatPath: true }}>
            <Routes>
            <Route path="/" element={<Layout />}>
              <Route index element={<HomePage />} />
              <Route path="events" element={<Events />} />
              <Route path="events/:id" element={<EventDetail />} />
              <Route path="my-events" element={<MyEvents />} />
              <Route path="campaigns" element={<Campaigns />} />
              <Route path="campaigns/:id" element={<CampaignDetail />} />
              <Route path="donation-success" element={<DonationSuccess />} />
              <Route path="profile" element={<ProfilePage />} />
              <Route path="dashboard" element={<Dashboard />} />
              <Route path="login" element={<LoginPage />} />
              <Route path="register" element={<RegisterPage />} />
              <Route path="posts" element={<Posts />} />
              <Route path="posts/:id" element={<PostDetail />} />
              <Route path="*" element={<NotFound />} />
            </Route>
            {/* Admin routes - separate from main layout but still need PostsProvider */}
            <Route path="/admin" element={<AdminDashboard />}>
              <Route index element={<DashboardOverview />} />
              <Route path="dashboard" element={<DashboardOverview />} />
              <Route path="posts" element={<ManagePosts />} />
              <Route path="comment-reports" element={<ManageCommentReports />} />
              <Route path="campaigns" element={<ManageCampaignsNew />} />
              <Route path="donations" element={<ManageDonations />} />
              <Route path="events" element={<ManageEvents />} />
              <Route path="notifications" element={<NotificationManagement />} />
              <Route path="users" element={<ManageUsers />} />
            </Route>
            <Route path="/admin/login" element={<AdminLogin />} />
            <Route path="payment/momo_return" element={<MomoReturnPage />} />
            <Route path="payment/payos_return" element={<PayOSReturnPage />} />
            </Routes>
          </BrowserRouter>
        </PostsProvider>
      </AuthProvider>
    </ThemeProvider>
  );
}

export default App;
