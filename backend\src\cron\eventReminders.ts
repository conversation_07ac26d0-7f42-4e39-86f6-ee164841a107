import cron from 'node-cron';
import Event from '../models/Event';
import EventRegistration from '../models/EventRegistration';
import { notifyEventReminder } from '../services/notification.service';

// Run every hour to check for event reminders
export const eventReminderJob = cron.schedule('0 * * * *', async () => {
  try {
    console.log('🔔 [Cron] Running event reminder job...');
    
    const now = new Date();
    const oneHourFromNow = new Date(now.getTime() + (60 * 60 * 1000));
    const oneDayFromNow = new Date(now.getTime() + (24 * 60 * 60 * 1000));
    const sevenDaysFromNow = new Date(now.getTime() + (7 * 24 * 60 * 60 * 1000));
    
    // Find events starting in 1 hour
    const eventsIn1Hour = await Event.find({
      status: 'active',
      startDate: {
        $gte: now,
        $lte: oneHourFromNow
      }
    });
    
    // Find events starting in 1 day (check within 1-hour window around 24 hours)
    const eventsIn1Day = await Event.find({
      status: 'active',
      startDate: {
        $gte: oneDayFromNow,
        $lte: new Date(oneDayFromNow.getTime() + (60 * 60 * 1000))
      }
    });
    
    // Find events starting in 7 days (check within 1-hour window around 7 days)
    const eventsIn7Days = await Event.find({
      status: 'active',
      startDate: {
        $gte: sevenDaysFromNow,
        $lte: new Date(sevenDaysFromNow.getTime() + (60 * 60 * 1000))
      }
    });
    
    // Process 1-hour reminders
    for (const event of eventsIn1Hour) {
      const registrations = await EventRegistration.find({
        eventId: event._id,
        status: 'registered'
      }).distinct('userId');
      
      if (registrations.length > 0) {
        await notifyEventReminder(
          registrations,
          event.title,
          event._id,
          event.startDate,
          '1hour'
        );
        console.log(`📧 [Cron] 1-hour reminder sent for event: ${event.title} (${registrations.length} participants)`);
      }
    }
    
    // Process 1-day reminders
    for (const event of eventsIn1Day) {
      const registrations = await EventRegistration.find({
        eventId: event._id,
        status: 'registered'
      }).distinct('userId');
      
      if (registrations.length > 0) {
        await notifyEventReminder(
          registrations,
          event.title,
          event._id,
          event.startDate,
          '1day'
        );
        console.log(`📧 [Cron] 1-day reminder sent for event: ${event.title} (${registrations.length} participants)`);
      }
    }
    
    // Process 7-day reminders
    for (const event of eventsIn7Days) {
      const registrations = await EventRegistration.find({
        eventId: event._id,
        status: 'registered'
      }).distinct('userId');
      
      if (registrations.length > 0) {
        await notifyEventReminder(
          registrations,
          event.title,
          event._id,
          event.startDate,
          '7days'
        );
        console.log(`📧 [Cron] 7-day reminder sent for event: ${event.title} (${registrations.length} participants)`);
      }
    }
    
    const totalEvents = eventsIn1Hour.length + eventsIn1Day.length + eventsIn7Days.length;
    console.log(`✅ [Cron] Event reminders completed. Processed ${totalEvents} events`);
    
  } catch (error) {
    console.error('❌ [Cron] Error in event reminder job:', error);
  }
}, {
  scheduled: false // Don't start automatically
});

// Start the cron job
export const startEventReminderJob = () => {
  eventReminderJob.start();
  console.log('⏰ [Cron] Event reminder job started (hourly)');
};
