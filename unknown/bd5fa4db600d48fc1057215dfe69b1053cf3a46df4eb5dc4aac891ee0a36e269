const t=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],e=(()=>new Set(t))();const s={};class n{constructor(){this.subscriptions=[]}add(t){var e,s;return e=this.subscriptions,s=t,-1===e.indexOf(s)&&e.push(s),()=>function(t,e){const s=t.indexOf(e);s>-1&&t.splice(s,1)}(this.subscriptions,t)}notify(t,e,s){const n=this.subscriptions.length;if(n)if(1===n)this.subscriptions[0](t,e,s);else for(let r=0;r<n;r++){const n=this.subscriptions[r];n&&n(t,e,s)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}const r=["setup","read","resolveKeyframes","preUpdate","update","preRender","render","postRender"],i={value:null,addProjectionMetrics:null};function o(t,e){let n=!1,o=!0;const a={delta:0,timestamp:0,isProcessing:!1},c=()=>n=!0,h=r.reduce(((t,s)=>(t[s]=function(t,e){let s=new Set,n=new Set,r=!1,o=!1;const a=new WeakSet;let c={delta:0,timestamp:0,isProcessing:!1},h=0;function d(e){a.has(e)&&(p.schedule(e),t()),h++,e(c)}const p={schedule:(t,e=!1,i=!1)=>{const o=i&&r?s:n;return e&&a.add(t),o.has(t)||o.add(t),t},cancel:t=>{n.delete(t),a.delete(t)},process:t=>{c=t,r?o=!0:(r=!0,[s,n]=[n,s],s.forEach(d),e&&i.value&&i.value.frameloop[e].push(h),h=0,s.clear(),r=!1,o&&(o=!1,p.process(t)))}};return p}(c,e?s:void 0),t)),{}),{setup:d,read:p,resolveKeyframes:u,preUpdate:l,update:f,preRender:m,render:v,postRender:g}=h,y=()=>{const r=s.useManualTiming?a.timestamp:performance.now();n=!1,s.useManualTiming||(a.delta=o?1e3/60:Math.max(Math.min(r-a.timestamp,40),1)),a.timestamp=r,a.isProcessing=!0,d.process(a),p.process(a),u.process(a),l.process(a),f.process(a),m.process(a),v.process(a),g.process(a),a.isProcessing=!1,n&&e&&(o=!1,t(y))};return{schedule:r.reduce(((e,s)=>{const r=h[s];return e[s]=(e,s=!1,i=!1)=>(n||(n=!0,o=!0,a.isProcessing||t(y)),r.schedule(e,s,i)),e}),{}),cancel:t=>{for(let e=0;e<r.length;e++)h[r[e]].cancel(t)},state:a,steps:h}}const{schedule:a,cancel:c,state:h,steps:d}=o("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:t=>t,!0);let p;function u(){p=void 0}const l={now:()=>(void 0===p&&l.set(h.isProcessing||s.useManualTiming?h.timestamp:performance.now()),p),set:t=>{p=t,queueMicrotask(u)}};class f{constructor(t,e={}){this.canTrackVelocity=null,this.events={},this.updateAndNotify=(t,e=!0)=>{const s=l.now();if(this.updatedAt!==s&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(t),this.current!==this.prev&&(this.events.change?.notify(this.current),this.dependents))for(const t of this.dependents)t.dirty();e&&this.events.renderRequest?.notify(this.current)},this.hasAnimated=!1,this.setCurrent(t),this.owner=e.owner}setCurrent(t){var e;this.current=t,this.updatedAt=l.now(),null===this.canTrackVelocity&&void 0!==t&&(this.canTrackVelocity=(e=this.current,!isNaN(parseFloat(e))))}setPrevFrameValue(t=this.current){this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt}onChange(t){return this.on("change",t)}on(t,e){this.events[t]||(this.events[t]=new n);const s=this.events[t].add(e);return"change"===t?()=>{s(),a.read((()=>{this.events.change.getSize()||this.stop()}))}:s}clearListeners(){for(const t in this.events)this.events[t].clear()}attach(t,e){this.passiveEffect=t,this.stopPassiveEffect=e}set(t,e=!0){e&&this.passiveEffect?this.passiveEffect(t,this.updateAndNotify):this.updateAndNotify(t,e)}setWithVelocity(t,e,s){this.set(e),this.prev=void 0,this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt-s}jump(t,e=!0){this.updateAndNotify(t),this.prev=t,this.prevUpdatedAt=this.prevFrameValue=void 0,e&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}dirty(){this.events.change?.notify(this.current)}addDependent(t){this.dependents||(this.dependents=new Set),this.dependents.add(t)}removeDependent(t){this.dependents&&this.dependents.delete(t)}get(){return this.current}getPrevious(){return this.prev}getVelocity(){const t=l.now();if(!this.canTrackVelocity||void 0===this.prevFrameValue||t-this.updatedAt>30)return 0;const e=Math.min(this.updatedAt-this.prevUpdatedAt,30);return s=parseFloat(this.current)-parseFloat(this.prevFrameValue),(n=e)?s*(1e3/n):0;var s,n}start(t){return this.stop(),new Promise((e=>{this.hasAnimated=!0,this.animation=t(e),this.events.animationStart&&this.events.animationStart.notify()})).then((()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()}))}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.dependents?.clear(),this.events.destroy?.notify(),this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function m(t){return(e,s)=>{const n=function(t,e,s){if(t instanceof EventTarget)return[t];if("string"==typeof t){let n=document;e&&(n=e.current);const r=s?.[t]??n.querySelectorAll(t);return r?Array.from(r):[]}return Array.from(t)}(e),r=[];for(const e of n){const n=t(e,s);r.push(n)}return()=>{for(const t of r)t()}}}const v={test:t=>"number"==typeof t,parse:parseFloat,transform:t=>t},g={...v,transform:t=>((t,e,s)=>s>e?e:s<t?t:s)(0,1,t)},y={...v,default:1},w={...v,transform:Math.round},b=t=>({test:e=>"string"==typeof e&&e.endsWith(t)&&1===e.split(" ").length,parse:parseFloat,transform:e=>`${e}${t}`}),A=b("deg"),P=b("%"),R=b("px"),k=(()=>({...P,parse:t=>P.parse(t)/100,transform:t=>P.transform(100*t)}))(),F={borderWidth:R,borderTopWidth:R,borderRightWidth:R,borderBottomWidth:R,borderLeftWidth:R,borderRadius:R,radius:R,borderTopLeftRadius:R,borderTopRightRadius:R,borderBottomRightRadius:R,borderBottomLeftRadius:R,width:R,maxWidth:R,height:R,maxHeight:R,top:R,right:R,bottom:R,left:R,padding:R,paddingTop:R,paddingRight:R,paddingBottom:R,paddingLeft:R,margin:R,marginTop:R,marginRight:R,marginBottom:R,marginLeft:R,backgroundPositionX:R,backgroundPositionY:R,...{rotate:A,rotateX:A,rotateY:A,rotateZ:A,scale:y,scaleX:y,scaleY:y,scaleZ:y,skew:A,skewX:A,skewY:A,distance:R,translateX:R,translateY:R,translateZ:R,x:R,y:R,z:R,perspective:R,transformPerspective:R,opacity:g,originX:k,originY:k,originZ:R},zIndex:w,fillOpacity:g,strokeOpacity:g,numOctaves:w};class x{constructor(){this.latest={},this.values=new Map}set(t,e,s,n,r=!0){const i=this.values.get(t);i&&i.onRemove();const o=()=>{const n=e.get();this.latest[t]=r?((t,e)=>e&&"number"==typeof t?e.transform(t):t)(n,F[t]):n,s&&a.render(s)};o();const h=e.on("change",o);n&&e.addDependent(n);const d=()=>{h(),s&&c(s),this.values.delete(t),n&&e.removeDependent(n)};return this.values.set(t,{value:e,onRemove:d}),d}get(t){return this.values.get(t)?.value}destroy(){for(const t of this.values.values())t.onRemove()}}function T(t){const e=new WeakMap,s=[];return(n,r)=>{const i=e.get(n)??new x;e.set(n,i);for(const e in r){const o=r[e],a=t(n,i,e,o);s.push(a)}return()=>{for(const t of s)t()}}}const V={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"};const X=new Set(["originX","originY","originZ"]),Y=(s,n,r,i)=>{let o,a;return e.has(r)?(n.get("transform")||(function(t){return"object"==typeof(e=t)&&null!==e&&"offsetHeight"in t;var e}(s)||n.get("transformBox")||Y(s,n,"transformBox",new f("fill-box")),n.set("transform",new f("none"),(()=>{s.style.transform=function(e){let s="",n=!0;for(let r=0;r<t.length;r++){const i=t[r],o=e.latest[i];if(void 0===o)continue;let a=!0;a="number"==typeof o?o===(i.startsWith("scale")?1:0):0===parseFloat(o),a||(n=!1,s+=`${V[i]||i}(${e.latest[i]}) `)}return n?"none":s.trim()}(n)}))),a=n.get("transform")):X.has(r)?(n.get("transformOrigin")||n.set("transformOrigin",new f(""),(()=>{const t=n.latest.originX??"50%",e=n.latest.originY??"50%",r=n.latest.originZ??0;s.style.transformOrigin=`${t} ${e} ${r}`})),a=n.get("transformOrigin")):o=r.startsWith("--")?()=>{s.style.setProperty(r,n.latest[r])}:()=>{s.style[r]=n.latest[r]},n.set(r,i,o,a)},W=m(T(Y));export{Y as addStyleValue,W as styleEffect};
