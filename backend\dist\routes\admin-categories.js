"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const Campaign_1 = require("../models/Campaign");
const router = express_1.default.Router();
// Temporary endpoint to add categories to existing campaigns
router.post('/add-categories', (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        console.log('🔄 Bắt đầu thêm danh mục cho các chiến dịch...');
        const categories = [
            'Giáo dục',
            'Y tế',
            'Trẻ em',
            'Người già',
            'Môi trường',
            'Thiên tai',
            'Động vật',
            'Cộng đồng',
            'Văn hóa',
            'Thể thao'
        ];
        // Lấy tất cả campaigns
        const campaigns = yield Campaign_1.Campaign.find({});
        console.log(`📊 Tìm thấy ${campaigns.length} chiến dịch`);
        if (campaigns.length === 0) {
            return res.json({
                success: false,
                message: 'Không có chiến dịch nào để cập nhật'
            });
        }
        let updatedCount = 0;
        // Cập nhật từng campaign với category ngẫu nhiên
        for (let i = 0; i < campaigns.length; i++) {
            const campaign = campaigns[i];
            // Chỉ thêm category nếu chưa có hoặc category là 'Khác'
            if (!campaign.category || campaign.category === 'Khác') {
                const randomCategory = categories[Math.floor(Math.random() * categories.length)];
                yield Campaign_1.Campaign.findByIdAndUpdate(campaign._id, {
                    category: randomCategory
                });
                console.log(`✅ Cập nhật "${campaign.title}" với danh mục: ${randomCategory}`);
                updatedCount++;
            }
            else {
                console.log(`⏭️  "${campaign.title}" đã có danh mục: ${campaign.category}`);
            }
        }
        console.log(`🎉 Hoàn thành! Đã cập nhật ${updatedCount} chiến dịch`);
        // Hiển thị thống kê
        const updatedCampaigns = yield Campaign_1.Campaign.find({});
        const categoryStats = {};
        updatedCampaigns.forEach(campaign => {
            if (campaign.category) {
                categoryStats[campaign.category] = (categoryStats[campaign.category] || 0) + 1;
            }
        });
        console.log('\n📈 Thống kê danh mục:');
        Object.entries(categoryStats).forEach(([category, count]) => {
            console.log(`   ${category}: ${count} chiến dịch`);
        });
        res.json({
            success: true,
            message: `Đã cập nhật ${updatedCount} chiến dịch với danh mục`,
            stats: categoryStats,
            totalCampaigns: campaigns.length,
            updatedCampaigns: updatedCount
        });
    }
    catch (error) {
        console.error('❌ Lỗi khi thêm danh mục:', error);
        res.status(500).json({
            success: false,
            message: 'Lỗi khi thêm danh mục',
            error: error instanceof Error ? error.message : 'Unknown error'
        });
    }
}));
// Get category statistics
router.get('/category-stats', (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const campaigns = yield Campaign_1.Campaign.find({});
        const categoryStats = {};
        campaigns.forEach(campaign => {
            if (campaign.category) {
                categoryStats[campaign.category] = (categoryStats[campaign.category] || 0) + 1;
            }
        });
        res.json({
            success: true,
            stats: categoryStats,
            totalCampaigns: campaigns.length
        });
    }
    catch (error) {
        console.error('❌ Lỗi khi lấy thống kê danh mục:', error);
        res.status(500).json({
            success: false,
            message: 'Lỗi khi lấy thống kê danh mục',
            error: error instanceof Error ? error.message : 'Unknown error'
        });
    }
}));
exports.default = router;
