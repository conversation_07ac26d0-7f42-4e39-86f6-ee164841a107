import express from 'express';
import {
  getUserNotifications,
  mark<PERSON><PERSON><PERSON>,
  markAll<PERSON><PERSON><PERSON>,
  getUnreadCount,
  createAdminNotification,
  getNotificationStats
} from '../controllers/notificationController';
import { protect } from '../middleware/authMiddleware';

const router = express.Router();

// Get unread notifications count (no auth required for polling)
router.get('/unread-count', protect, getUnreadCount);

// Apply protect middleware to routes that need authentication
// Get user's notifications
router.get('/', protect, getUserNotifications);

// Mark notification as read
router.patch('/:notificationId/read', protect, markAsRead);

// Mark all notifications as read
router.patch('/read-all', protect, markAllAsRead);

// Admin routes
router.post('/admin/create', protect, createAdminNotification);
router.get('/admin/stats', protect, getNotificationStats);

export default router;