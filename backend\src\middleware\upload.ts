import multer from 'multer';
import path from 'path';
import fs from 'fs';
import { v4 as uuidv4 } from 'uuid';

// <PERSON><PERSON><PERSON> b<PERSON><PERSON> thư mục uploads tồn tại
const uploadDir = path.join(__dirname, '../uploads');
if (!fs.existsSync(uploadDir)) {
  fs.mkdirSync(uploadDir, { recursive: true });
}

// Cấu hình storage cho multer
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    try {
      console.log('📁 [Upload] Processing file upload for path:', req.path, 'method:', req.method);

      // Tạo thư mục riêng cho chiến dịch nếu đang tạo mới hoặc cập nhật
      if (req.path.includes('/admin') && (req.method === 'POST' || req.method === 'PUT')) {
        const campaignId = req.method === 'POST' ? uuidv4() : req.params.id || uuidv4();
        const campaignDir = path.join(uploadDir, campaignId);
        console.log('📁 [Upload] Creating/using campaign directory:', campaignDir, 'for method:', req.method);
        fs.mkdirSync(campaignDir, { recursive: true });
        if (req.method === 'POST') {
          req.body.campaignId = campaignId; // Chỉ set campaignId cho POST
        }
        cb(null, campaignDir);
      }
      // Nếu đang cập nhật, sử dụng thư mục của chiến dịch đó
      else if (req.path.startsWith('/admin/') && req.method === 'PUT') {
        const campaignId = req.params.id;
        const campaignDir = path.join(uploadDir, campaignId);
        console.log('📁 [Upload] Using existing campaign directory:', campaignDir);
        if (!fs.existsSync(campaignDir)) {
          fs.mkdirSync(campaignDir, { recursive: true });
        }
        cb(null, campaignDir);
      }
      // Mặc định sử dụng thư mục uploads
      else {
        console.log('📁 [Upload] Using default upload directory:', uploadDir);
        cb(null, uploadDir);
      }
    } catch (error) {
      console.error('❌ [Upload] Error in destination function:', error);
      cb(error as Error, uploadDir); // Fallback to default directory
    }
  },
  filename: function (req, file, cb) {
    // Tạo tên file duy nhất với timestamp và uuid
    const uniqueSuffix = Date.now() + '-' + uuidv4();
    cb(null, file.fieldname + '-' + uniqueSuffix + path.extname(file.originalname));
  }
});

// Kiểm tra file type
const fileFilter = (req: any, file: Express.Multer.File, cb: multer.FileFilterCallback) => {
  console.log('🔍 [Upload] Checking file type:', file.mimetype, 'for file:', file.originalname);

  // Chỉ chấp nhận các file ảnh
  if (file.mimetype.startsWith('image/')) {
    console.log('✅ [Upload] File type accepted:', file.mimetype);
    cb(null, true);
  } else {
    console.log('❌ [Upload] File type rejected:', file.mimetype);
    cb(new Error('Chỉ chấp nhận file ảnh!'));
  }
};

// Cấu hình multer
const upload = multer({
  storage: storage,
  fileFilter: fileFilter,
  limits: {
    fileSize: 5 * 1024 * 1024, // Giới hạn 5MB
    files: 5 // Tối đa 5 file
  }
});

export default upload; 