"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getOnlineStatus = exports.deleteUser = exports.updateUserRole = exports.updateUserStatus = exports.getUserById = exports.getAllUsers = exports.deletePostTest = exports.getPostsTest = exports.getDashboardStats = exports.getPostComments = exports.resolveCommentReport = exports.getCommentReports = exports.resolveReport = exports.deleteComment = exports.getAllCampaigns = exports.getAllDonations = exports.deletePost = exports.getAllPosts = void 0;
const Post_1 = require("../models/Post");
const Comment_1 = require("../models/Comment");
const CommentReport_1 = require("../models/CommentReport");
const Report_1 = require("../models/Report");
const Donation_1 = require("../models/Donation");
const Campaign_1 = require("../models/Campaign");
const user_model_1 = require("../models/user.model");
const activity_tracker_1 = require("../utils/activity-tracker");
const user_ranking_1 = require("../utils/user-ranking");
const getAllPosts = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const page = parseInt(req.query.page) || 1;
        const limit = parseInt(req.query.limit) || 20;
        const skip = (page - 1) * limit;
        // Query filters
        const search = req.query.search;
        const status = req.query.status; // 'all', 'reported', 'recent'
        // Build query
        const query = {};
        if (search) {
            query.$or = [
                { content: { $regex: search, $options: 'i' } },
                { 'user.name': { $regex: search, $options: 'i' } }
            ];
        }
        if (status === 'reported') {
            query.reports = { $exists: true, $not: { $size: 0 } };
        }
        else if (status === 'recent') {
            const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);
            query.createdAt = { $gte: oneDayAgo };
        }
        console.log('🔍 [Admin] Query:', query);
        // Use populate instead of aggregation for better reliability
        const posts = yield Post_1.Post.find(query)
            .populate('user', 'name email avatar')
            .populate({
            path: 'reports',
            select: 'reason user createdAt status',
            populate: {
                path: 'user',
                select: 'name email'
            }
        })
            .sort({ createdAt: -1 })
            .skip(skip)
            .limit(limit)
            .lean();
        console.log('✅ [Admin] Found posts:', posts.length);
        // Transform posts to include report count and status
        const transformedPosts = posts.map(post => {
            const reports = post.reports || [];
            const pendingReports = reports.filter((r) => r.status !== 'resolved');
            return Object.assign(Object.assign({}, post), { reportCount: reports.length, pendingReportCount: pendingReports.length, hasReports: reports.length > 0, hasPendingReports: pendingReports.length > 0, reportDetails: reports, latestReport: reports.length > 0 ? reports[reports.length - 1] : null });
        });
        console.log('✅ [Admin] Sample post with reports:', transformedPosts[0]);
        console.log('✅ [Admin] Posts with reports:', transformedPosts.filter(p => p.hasReports).length);
        const total = yield Post_1.Post.countDocuments(query);
        const totalPages = Math.ceil(total / limit);
        res.json({
            success: true,
            data: {
                posts: transformedPosts,
                pagination: {
                    currentPage: page,
                    totalPages,
                    totalPosts: total,
                    hasNext: page < totalPages,
                    hasPrev: page > 1
                }
            }
        });
    }
    catch (err) {
        console.error('Error fetching admin posts:', err);
        res.status(500).json({
            success: false,
            message: 'Lỗi khi tải danh sách bài viết',
            error: err.message
        });
    }
});
exports.getAllPosts = getAllPosts;
const deletePost = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    var _a;
    try {
        const { id } = req.params;
        if (!id || !id.match(/^[0-9a-fA-F]{24}$/)) {
            return res.status(400).json({
                success: false,
                message: 'ID bài viết không hợp lệ'
            });
        }
        const post = yield Post_1.Post.findById(id).populate('user', 'name email');
        if (!post) {
            return res.status(404).json({
                success: false,
                message: 'Không tìm thấy bài viết'
            });
        }
        // Delete the post
        yield Post_1.Post.findByIdAndDelete(id);
        res.json({
            success: true,
            message: 'Đã xóa bài viết thành công',
            data: {
                deletedPost: {
                    _id: post._id,
                    content: post.content.substring(0, 50) + '...',
                    author: (_a = post.user) === null || _a === void 0 ? void 0 : _a.name
                }
            }
        });
    }
    catch (err) {
        console.error('Error deleting post:', err);
        res.status(500).json({
            success: false,
            message: 'Lỗi khi xóa bài viết',
            error: err.message
        });
    }
});
exports.deletePost = deletePost;
const getAllDonations = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const page = parseInt(req.query.page) || 1;
        const limit = parseInt(req.query.limit) || 50;
        const skip = (page - 1) * limit;
        const donations = yield Donation_1.Donation.find()
            .populate('campaignId', 'title')
            .sort({ createdAt: -1 })
            .skip(skip)
            .limit(limit);
        // Transform data to match frontend interface
        const transformedDonations = donations.map(donation => ({
            _id: donation._id,
            name: donation.name,
            email: donation.email,
            amount: donation.amount,
            message: donation.message || '',
            paymentMethod: donation.paymentMethod.toLowerCase(),
            status: donation.status,
            transactionId: donation.transactionId,
            createdAt: donation.createdAt,
            campaign: {
                _id: donation.campaignId._id,
                title: donation.campaignId.title
            },
            isAnonymous: donation.isAnonymous || false
        }));
        const total = yield Donation_1.Donation.countDocuments();
        res.json({
            success: true,
            donations: transformedDonations,
            pagination: {
                page,
                limit,
                total,
                pages: Math.ceil(total / limit)
            }
        });
    }
    catch (err) {
        console.error('Error fetching donations:', err);
        res.status(500).json({ success: false, message: err.message });
    }
});
exports.getAllDonations = getAllDonations;
const getAllCampaigns = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const page = parseInt(req.query.page) || 1;
        const limit = parseInt(req.query.limit) || 50;
        const skip = (page - 1) * limit;
        const campaigns = yield Campaign_1.Campaign.find()
            .populate('createdBy', 'name email')
            .sort({ createdAt: -1 })
            .skip(skip)
            .limit(limit);
        const total = yield Campaign_1.Campaign.countDocuments();
        res.json({
            success: true,
            campaigns: campaigns,
            pagination: {
                page,
                limit,
                total,
                pages: Math.ceil(total / limit)
            }
        });
    }
    catch (err) {
        console.error('Error fetching campaigns:', err);
        res.status(500).json({ success: false, message: err.message });
    }
});
exports.getAllCampaigns = getAllCampaigns;
const deleteComment = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { postId, commentId } = req.params;
        const post = yield Post_1.Post.findById(postId);
        if (!post) {
            return res.status(404).json({
                success: false,
                message: 'Bài viết không tồn tại'
            });
        }
        // Remove comment from post
        const commentIndex = post.comments.findIndex((comment) => comment._id.toString() === commentId);
        if (commentIndex === -1) {
            return res.status(404).json({
                success: false,
                message: 'Bình luận không tồn tại'
            });
        }
        post.comments.splice(commentIndex, 1);
        yield post.save();
        res.json({
            success: true,
            message: 'Đã xóa bình luận thành công'
        });
    }
    catch (error) {
        console.error('Error deleting comment:', error);
        res.status(500).json({
            success: false,
            message: 'Lỗi server khi xóa bình luận'
        });
    }
});
exports.deleteComment = deleteComment;
const resolveReport = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const postId = req.params.id;
        const { action } = req.body; // 'dismiss' or 'remove'
        const post = yield Post_1.Post.findById(postId);
        if (!post) {
            return res.status(404).json({
                success: false,
                message: 'Bài viết không tồn tại'
            });
        }
        if (action === 'remove') {
            // Delete the post
            yield Post_1.Post.findByIdAndDelete(postId);
            res.json({
                success: true,
                message: 'Đã xóa bài viết và xử lý báo cáo'
            });
        }
        else if (action === 'dismiss') {
            // Clear all reports
            post.reports = [];
            yield post.save();
            res.json({
                success: true,
                message: 'Đã bỏ qua báo cáo'
            });
        }
        else {
            return res.status(400).json({
                success: false,
                message: 'Hành động không hợp lệ'
            });
        }
    }
    catch (error) {
        console.error('Error resolving report:', error);
        res.status(500).json({
            success: false,
            message: 'Lỗi server khi xử lý báo cáo'
        });
    }
});
exports.resolveReport = resolveReport;
// GET /admin/comment-reports - Lấy danh sách báo cáo comment
const getCommentReports = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const page = parseInt(req.query.page) || 1;
        const limit = parseInt(req.query.limit) || 20;
        const skip = (page - 1) * limit;
        const status = req.query.status || 'pending'; // pending, resolved, dismissed
        const query = {};
        if (status !== 'all') {
            query.status = status;
        }
        const reports = yield CommentReport_1.CommentReport.find(query)
            .populate('user', 'name email')
            .populate('comment', 'content')
            .populate('post', 'content')
            .sort({ createdAt: -1 })
            .skip(skip)
            .limit(limit);
        const total = yield CommentReport_1.CommentReport.countDocuments(query);
        res.json({
            success: true,
            data: {
                reports,
                pagination: {
                    currentPage: page,
                    totalPages: Math.ceil(total / limit),
                    totalReports: total,
                    hasNext: page < Math.ceil(total / limit),
                    hasPrev: page > 1
                }
            }
        });
    }
    catch (err) {
        console.error('Error fetching comment reports:', err);
        res.status(500).json({
            success: false,
            message: 'Lỗi khi tải danh sách báo cáo comment',
            error: err.message
        });
    }
});
exports.getCommentReports = getCommentReports;
// POST /admin/comment-reports/:reportId/resolve - Xử lý báo cáo comment
const resolveCommentReport = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    var _a;
    try {
        const { reportId } = req.params;
        const { action } = req.body; // 'dismiss', 'remove_comment'
        const adminId = (_a = req.user) === null || _a === void 0 ? void 0 : _a._id;
        const report = yield CommentReport_1.CommentReport.findById(reportId)
            .populate('comment')
            .populate('post');
        if (!report) {
            return res.status(404).json({
                success: false,
                message: 'Không tìm thấy báo cáo'
            });
        }
        if (action === 'remove_comment') {
            // Xóa comment và tất cả replies
            yield Comment_1.Comment.deleteMany({
                $or: [
                    { _id: report.comment },
                    { parentComment: report.comment }
                ]
            });
            // Cập nhật post để xóa comment khỏi danh sách
            yield Post_1.Post.updateOne({ comments: report.comment }, { $pull: { comments: report.comment } });
            // Cập nhật trạng thái báo cáo
            report.status = 'resolved';
            report.resolvedAt = new Date();
            report.resolvedBy = adminId;
            yield report.save();
            res.json({
                success: true,
                message: 'Đã xóa comment và xử lý báo cáo'
            });
        }
        else if (action === 'dismiss') {
            // Bỏ qua báo cáo
            report.status = 'dismissed';
            report.resolvedAt = new Date();
            report.resolvedBy = adminId;
            yield report.save();
            res.json({
                success: true,
                message: 'Đã bỏ qua báo cáo'
            });
        }
        else {
            return res.status(400).json({
                success: false,
                message: 'Hành động không hợp lệ'
            });
        }
    }
    catch (error) {
        console.error('Error resolving comment report:', error);
        res.status(500).json({
            success: false,
            message: 'Lỗi server khi xử lý báo cáo comment'
        });
    }
});
exports.resolveCommentReport = resolveCommentReport;
// GET /admin/posts/:postId/comments - Lấy danh sách comment của bài viết với báo cáo
const getPostComments = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { postId } = req.params;
        const page = parseInt(req.query.page) || 1;
        const limit = parseInt(req.query.limit) || 20;
        const skip = (page - 1) * limit;
        // Lấy comments với thông tin báo cáo
        const comments = yield Comment_1.Comment.find({ post: postId })
            .populate('user', 'name email avatar')
            .sort({ createdAt: -1 })
            .skip(skip)
            .limit(limit);
        // Lấy số lượng báo cáo cho mỗi comment
        const commentsWithReports = yield Promise.all(comments.map((comment) => __awaiter(void 0, void 0, void 0, function* () {
            const reportCount = yield CommentReport_1.CommentReport.countDocuments({
                comment: comment._id,
                status: 'pending'
            });
            const reports = yield CommentReport_1.CommentReport.find({
                comment: comment._id,
                status: 'pending'
            })
                .populate('user', 'name')
                .select('reason createdAt user')
                .sort({ createdAt: -1 });
            return Object.assign(Object.assign({}, comment.toObject()), { reportCount,
                reports });
        })));
        const total = yield Comment_1.Comment.countDocuments({ post: postId });
        res.json({
            success: true,
            data: {
                comments: commentsWithReports,
                pagination: {
                    currentPage: page,
                    totalPages: Math.ceil(total / limit),
                    totalComments: total,
                    hasNext: page < Math.ceil(total / limit),
                    hasPrev: page > 1
                }
            }
        });
    }
    catch (err) {
        console.error('Error fetching post comments:', err);
        res.status(500).json({
            success: false,
            message: 'Lỗi khi tải danh sách comment',
            error: err.message
        });
    }
});
exports.getPostComments = getPostComments;
// GET /admin/dashboard/stats - Lấy thống kê tổng quan cho dashboard
const getDashboardStats = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    var _a, _b;
    try {
        // Import Event model
        const Event = require('../models/Event').default;
        // Parallel queries for better performance
        const [totalUsers, totalPosts, totalCampaigns, totalEvents, totalDonationAmount, pendingReports, recentUsers, recentPosts, recentDonations] = yield Promise.all([
            user_model_1.User.countDocuments(),
            Post_1.Post.countDocuments(),
            Campaign_1.Campaign.countDocuments(),
            Event.countDocuments(),
            Donation_1.Donation.aggregate([
                { $match: { status: 'success' } },
                { $group: { _id: null, total: { $sum: '$amount' } } }
            ]),
            Report_1.Report.countDocuments(), // All post reports
            user_model_1.User.countDocuments({
                createdAt: { $gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) }
            }),
            Post_1.Post.countDocuments({
                createdAt: { $gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) }
            }),
            Donation_1.Donation.countDocuments({
                createdAt: { $gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) }
            })
        ]);
        // Simplified monthly data for charts (last 6 months)
        const monthlyData = [];
        const now = new Date();
        for (let i = 5; i >= 0; i--) {
            const month = now.getMonth() - i;
            const year = now.getFullYear();
            // Handle year rollover
            let targetMonth = month;
            let targetYear = year;
            if (month < 0) {
                targetMonth = 12 + month;
                targetYear = year - 1;
            }
            const startOfMonth = new Date(targetYear, targetMonth, 1);
            const endOfMonth = new Date(targetYear, targetMonth + 1, 0);
            console.log(`📅 Processing ${targetMonth + 1}/${targetYear}`);
            const [monthUsers, monthPosts, monthDonations, cumulativeUsers] = yield Promise.all([
                user_model_1.User.countDocuments({
                    createdAt: { $gte: startOfMonth, $lte: endOfMonth }
                }),
                Post_1.Post.countDocuments({
                    createdAt: { $gte: startOfMonth, $lte: endOfMonth }
                }),
                Donation_1.Donation.aggregate([
                    {
                        $match: {
                            createdAt: { $gte: startOfMonth, $lte: endOfMonth },
                            status: 'success'
                        }
                    },
                    { $group: { _id: null, total: { $sum: '$amount' } } }
                ]),
                user_model_1.User.countDocuments({
                    createdAt: { $lte: endOfMonth }
                })
            ]);
            monthlyData.push({
                month: `T${targetMonth + 1}`,
                users: monthUsers,
                posts: monthPosts,
                donations: ((_a = monthDonations[0]) === null || _a === void 0 ? void 0 : _a.total) || 0,
                cumulativeUsers: cumulativeUsers
            });
        }
        // Campaign status distribution
        const campaignStats = yield Campaign_1.Campaign.aggregate([
            {
                $group: {
                    _id: '$status',
                    count: { $sum: 1 }
                }
            }
        ]);
        const campaignData = [
            { name: 'Hoàn thành', value: 0, color: '#10B981' },
            { name: 'Đang diễn ra', value: 0, color: '#3B82F6' },
            { name: 'Sắp kết thúc', value: 0, color: '#F59E0B' },
            { name: 'Tạm dừng', value: 0, color: '#EF4444' }
        ];
        campaignStats.forEach(stat => {
            const index = campaignData.findIndex(item => {
                if (stat._id === 'completed')
                    return item.name === 'Hoàn thành';
                if (stat._id === 'active')
                    return item.name === 'Đang diễn ra';
                if (stat._id === 'ending_soon')
                    return item.name === 'Sắp kết thúc';
                if (stat._id === 'paused')
                    return item.name === 'Tạm dừng';
                return false;
            });
            if (index !== -1) {
                campaignData[index].value = stat.count;
            }
        });
        // Event status distribution
        const eventStats = yield Event.aggregate([
            {
                $group: {
                    _id: '$status',
                    count: { $sum: 1 }
                }
            }
        ]);
        const eventData = [
            { name: 'Sắp diễn ra', value: 0 },
            { name: 'Đang diễn ra', value: 0 },
            { name: 'Đã hoàn thành', value: 0 },
            { name: 'Đã hủy', value: 0 }
        ];
        eventStats.forEach((stat) => {
            const index = eventData.findIndex(item => {
                if (stat._id === 'upcoming' || stat._id === 'pending' || stat._id === 'approved')
                    return item.name === 'Sắp diễn ra';
                if (stat._id === 'ongoing')
                    return item.name === 'Đang diễn ra';
                if (stat._id === 'completed')
                    return item.name === 'Đã hoàn thành';
                if (stat._id === 'cancelled')
                    return item.name === 'Đã hủy';
                return false;
            });
            if (index !== -1) {
                eventData[index].value = stat.count;
            }
        });
        // Recent activity
        const recentActivity = [
            {
                time: '2 phút trước',
                action: 'Người dùng mới đăng ký',
                user: 'Hệ thống',
                count: recentUsers
            },
            {
                time: '5 phút trước',
                action: 'Bài viết mới được đăng',
                user: 'Cộng đồng',
                count: recentPosts
            },
            {
                time: '10 phút trước',
                action: 'Quyên góp mới',
                user: 'Nhà hảo tâm',
                count: recentDonations
            }
        ];
        // Calculate monthly growth based on cumulative users
        let monthlyGrowth = 0;
        if (monthlyData.length >= 2) {
            const currentMonth = monthlyData[monthlyData.length - 1];
            const previousMonth = monthlyData[monthlyData.length - 2];
            if (previousMonth.cumulativeUsers > 0) {
                monthlyGrowth = ((currentMonth.cumulativeUsers - previousMonth.cumulativeUsers) / previousMonth.cumulativeUsers) * 100;
                monthlyGrowth = Math.round(monthlyGrowth * 10) / 10; // Round to 1 decimal place
            }
        }
        const stats = {
            totalUsers,
            totalPosts,
            totalEvents,
            totalDonations: ((_b = totalDonationAmount[0]) === null || _b === void 0 ? void 0 : _b.total) || 0,
            totalCampaigns,
            activeReports: pendingReports,
            monthlyGrowth
        };
        res.json({
            success: true,
            data: {
                stats,
                monthlyData,
                campaignData,
                eventData,
                recentActivity
            }
        });
    }
    catch (err) {
        console.error('❌ [Admin] Error fetching dashboard stats:', err);
        res.status(500).json({
            success: false,
            message: 'Lỗi khi tải thống kê dashboard',
            error: err.message
        });
    }
});
exports.getDashboardStats = getDashboardStats;
// Test endpoint to get posts without authentication
const getPostsTest = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const posts = yield Post_1.Post.find()
            .populate('user', 'name email')
            .sort({ createdAt: -1 })
            .limit(20);
        console.log(`✅ [Admin Test] Found ${posts.length} posts`);
        res.json({
            success: true,
            message: 'Lấy danh sách bài viết thành công',
            data: {
                posts,
                total: posts.length
            }
        });
    }
    catch (error) {
        console.error('❌ [Admin Test] Error getting posts:', error);
        res.status(500).json({
            success: false,
            message: 'Lỗi khi lấy danh sách bài viết',
            error: error instanceof Error ? error.message : 'Unknown error'
        });
    }
});
exports.getPostsTest = getPostsTest;
// Test endpoint to delete post without authentication
const deletePostTest = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    var _a;
    try {
        const { id } = req.params;
        console.log('🗑️ [Admin Test] Deleting post:', id);
        if (!id || !id.match(/^[0-9a-fA-F]{24}$/)) {
            return res.status(400).json({
                success: false,
                message: 'ID bài viết không hợp lệ'
            });
        }
        // Find the post first
        const post = yield Post_1.Post.findById(id).populate('user', 'name email');
        if (!post) {
            return res.status(404).json({
                success: false,
                message: 'Không tìm thấy bài viết'
            });
        }
        // Delete the post
        yield Post_1.Post.findByIdAndDelete(id);
        console.log('✅ [Admin Test] Post deleted successfully');
        res.json({
            success: true,
            message: 'Đã xóa bài viết thành công (test)',
            data: {
                deletedPost: {
                    _id: post._id,
                    content: post.content.substring(0, 50) + '...',
                    author: (_a = post.user) === null || _a === void 0 ? void 0 : _a.name
                }
            }
        });
    }
    catch (error) {
        console.error('❌ [Admin Test] Error deleting post:', error);
        res.status(500).json({
            success: false,
            message: 'Lỗi khi xóa bài viết',
            error: error instanceof Error ? error.message : 'Unknown error'
        });
    }
});
exports.deletePostTest = deletePostTest;
// Activity tracking functions are now imported from utils/activity-tracker.ts
// Helper functions for user activity
const getLastSeenText = (lastActive, userId) => {
    // Check real-time activity first
    if (userId && (0, activity_tracker_1.isUserCurrentlyOnline)(userId)) {
        return 'Đang hoạt động';
    }
    // Check real-time last seen
    if (userId) {
        const realTimeLastSeen = (0, activity_tracker_1.getUserLastSeen)(userId);
        if (realTimeLastSeen) {
            const now = new Date();
            const diffMinutes = (now.getTime() - realTimeLastSeen.getTime()) / (1000 * 60);
            if (diffMinutes < 1)
                return 'Vừa xem';
            if (diffMinutes < 5)
                return 'Vừa rời đi';
            if (diffMinutes < 60)
                return `${Math.floor(diffMinutes)} phút trước`;
            const diffHours = diffMinutes / 60;
            if (diffHours < 24)
                return `${Math.floor(diffHours)} giờ trước`;
        }
    }
    // Fallback to database lastActive
    if (!lastActive)
        return 'Chưa có thông tin';
    try {
        const now = new Date();
        const lastActiveDate = new Date(lastActive);
        const diffMinutes = (now.getTime() - lastActiveDate.getTime()) / (1000 * 60);
        if (diffMinutes < 60)
            return `${Math.floor(diffMinutes)} phút trước`;
        const diffHours = diffMinutes / 60;
        if (diffHours < 24)
            return `${Math.floor(diffHours)} giờ trước`;
        const diffDays = diffHours / 24;
        if (diffDays < 7)
            return `${Math.floor(diffDays)} ngày trước`;
        return formatDate(lastActive);
    }
    catch (error) {
        console.warn('Error getting last seen text:', error);
        return 'Chưa có thông tin';
    }
};
const formatDate = (dateString) => {
    if (!dateString)
        return 'Chưa có thông tin';
    try {
        return new Date(dateString).toLocaleDateString('vi-VN', {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    }
    catch (error) {
        return 'Chưa có thông tin';
    }
};
// User Management Functions
// GET /admin/users - Lấy danh sách tất cả người dùng
const getAllUsers = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        console.log('👥 [TypeScript Admin] ===== STARTING getAllUsers =====');
        // Validate and sanitize input parameters (same as professional server)
        const page = Math.max(1, parseInt(req.query.page) || 1);
        const limit = Math.min(100, Math.max(1, parseInt(req.query.limit) || 20));
        const search = (req.query.search || '').trim();
        const role = req.query.role || 'all';
        const status = req.query.status || 'all';
        console.log('👥 [TypeScript Admin] Parameters:', { page, limit, search, role, status });
        // Validate role parameter
        if (role !== 'all' && !['user', 'admin'].includes(role)) {
            return res.status(400).json({
                success: false,
                message: 'Tham số role không hợp lệ'
            });
        }
        // Validate status parameter
        if (status !== 'all' && !['active', 'inactive'].includes(status)) {
            return res.status(400).json({
                success: false,
                message: 'Tham số status không hợp lệ'
            });
        }
        // Build query (same as professional server)
        let query = {};
        if (search) {
            query.$or = [
                { name: { $regex: search, $options: 'i' } },
                { email: { $regex: search, $options: 'i' } }
            ];
        }
        if (role !== 'all') {
            query.role = role;
        }
        if (status !== 'all') {
            query.isActive = status === 'active';
        }
        const skip = (page - 1) * limit;
        console.log('👥 [TypeScript Admin] Query:', query);
        console.log('👥 [TypeScript Admin] Skip:', skip, 'Limit:', limit);
        const users = yield user_model_1.User.find(query)
            .select('-password')
            .sort({ createdAt: -1 })
            .skip(skip)
            .limit(limit);
        const totalUsers = yield user_model_1.User.countDocuments(query);
        const totalPages = Math.ceil(totalUsers / limit);
        // Add activity status and real statistics with real-time online tracking
        const usersWithActivity = yield Promise.all(users.map((user) => __awaiter(void 0, void 0, void 0, function* () {
            var _a, _b;
            // Get real statistics for each user - Fix donation counting by email
            const [postCount, donationCount] = yield Promise.all([
                Post_1.Post.countDocuments({ user: user._id }),
                Donation_1.Donation.countDocuments({ email: user.email, status: 'success' })
            ]);
            // Calculate activity status
            const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
            const userId = user._id.toString();
            // Use the actual isActive field from database, fallback to true
            const isActiveFromDB = user.hasOwnProperty('isActive') ? user.isActive : true;
            const isRecentlyActive = user.updatedAt > thirtyDaysAgo;
            // Real-time online status
            const isOnline = (0, activity_tracker_1.isUserCurrentlyOnline)(userId);
            const realTimeLastSeen = (0, activity_tracker_1.getUserLastSeen)(userId);
            // Calculate comprehensive stats - Fix donation counting by email
            const donationCountByEmail = yield Donation_1.Donation.countDocuments({
                email: user.email,
                status: 'success'
            });
            const stats = {
                totalPosts: postCount,
                totalDonations: donationCountByEmail,
                totalEventRegistrations: 0 // Will be implemented when EventRegistration is available
            };
            // Calculate activity score using standardized formula
            const activityScore = (0, user_ranking_1.calculateActivityScore)(stats);
            // Get user rank and badges
            const userRank = (0, user_ranking_1.getUserRank)(activityScore);
            const userBadges = (0, user_ranking_1.getUserBadges)(Object.assign(Object.assign({}, stats), { activityScore, isVerified: user.role === 'admin' || postCount > 5 }));
            const pointsToNext = (0, user_ranking_1.getPointsToNextRank)(activityScore);
            return Object.assign(Object.assign({}, user.toObject()), { isActive: isActiveFromDB, isOnline: isOnline, isRecentlyActive: isRecentlyActive, lastActive: realTimeLastSeen || user.updatedAt, joinDate: user.createdAt, lastSeenText: getLastSeenText(user.updatedAt, userId), totalNotifications: ((_a = user.notifications) === null || _a === void 0 ? void 0 : _a.length) || 0, unreadNotifications: ((_b = user.notifications) === null || _b === void 0 ? void 0 : _b.filter((n) => !n.read).length) || 0, activityScore: activityScore, isVerified: user.role === 'admin' || postCount > 5, stats: stats, 
                // New ranking system fields
                rank: userRank, badges: userBadges, pointsToNextRank: pointsToNext.needed, nextRank: pointsToNext.nextRank });
        })));
        console.log(`✅ [TypeScript Admin] Processed ${usersWithActivity.length} users with activity data`);
        // Get online users info for debugging
        const onlineInfo = (0, activity_tracker_1.getOnlineUsersInfo)();
        console.log(`🟢 [Activity] Currently online: ${onlineInfo.count} users`);
        // Get ranking statistics
        const rankingStats = (0, user_ranking_1.getRankingStats)(usersWithActivity);
        res.json({
            success: true,
            data: {
                users: usersWithActivity,
                pagination: {
                    currentPage: page,
                    totalPages,
                    totalUsers,
                    hasNext: page < totalPages,
                    hasPrev: page > 1,
                    limit
                },
                onlineStats: {
                    totalOnline: onlineInfo.count,
                    onlineUserIds: (0, activity_tracker_1.getOnlineUserIds)()
                },
                rankingStats: rankingStats
            },
            message: `Đã tải ${users.length} người dùng thành công`
        });
    }
    catch (err) {
        console.error('❌ [TypeScript Admin] Error fetching users:', err);
        res.status(500).json({
            success: false,
            message: 'Lỗi khi tải danh sách người dùng',
            error: err.message
        });
    }
});
exports.getAllUsers = getAllUsers;
// GET /admin/users/:id - Lấy thông tin chi tiết một người dùng
const getUserById = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    var _a, _b;
    try {
        const { id } = req.params;
        console.log(`👤 [Admin] Fetching user details for ID: ${id}`);
        const user = yield user_model_1.User.findById(id).select('-password').lean();
        if (!user) {
            return res.status(404).json({
                success: false,
                message: 'Không tìm thấy người dùng'
            });
        }
        // Get additional statistics - Fix donation counting by email
        const [postCount, donationCount] = yield Promise.all([
            Post_1.Post.countDocuments({ user: id }),
            Donation_1.Donation.countDocuments({ email: user.email, status: 'success' })
        ]);
        // Calculate activity status (same logic as getAllUsers)
        const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
        const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000);
        const isActiveFromDB = user.hasOwnProperty('isActive') ? user.isActive : true;
        const isRecentlyActive = user.updatedAt > thirtyDaysAgo;
        const isOnline = user.updatedAt > fiveMinutesAgo;
        const userWithStats = Object.assign(Object.assign({}, user), { isActive: isActiveFromDB, isOnline: isOnline, isRecentlyActive: isRecentlyActive, lastActive: user.updatedAt, joinDate: user.createdAt, lastSeenText: getLastSeenText(user.updatedAt), totalNotifications: ((_a = user.notifications) === null || _a === void 0 ? void 0 : _a.length) || 0, unreadNotifications: ((_b = user.notifications) === null || _b === void 0 ? void 0 : _b.filter((n) => !n.read).length) || 0, activityScore: postCount * 10 + donationCount * 20, isVerified: user.role === 'admin' || postCount > 5, stats: {
                totalPosts: postCount,
                totalDonations: donationCount,
                totalEventRegistrations: 0 // Will be implemented when EventRegistration is available
            } });
        console.log(`✅ [Admin] User details loaded for: ${user.name}`);
        res.json({
            success: true,
            data: userWithStats
        });
    }
    catch (err) {
        console.error('❌ [Admin] Error fetching user details:', err);
        res.status(500).json({
            success: false,
            message: 'Lỗi khi tải thông tin người dùng',
            error: err.message
        });
    }
});
exports.getUserById = getUserById;
// PUT /admin/users/:id/status - Cập nhật trạng thái người dùng
const updateUserStatus = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { id } = req.params;
        const { isActive } = req.body;
        console.log(`🔄 [Admin] Updating user status for ID: ${id}, active: ${isActive}`);
        const user = yield user_model_1.User.findById(id);
        if (!user) {
            return res.status(404).json({
                success: false,
                message: 'Không tìm thấy người dùng'
            });
        }
        // Update the isActive field properly using findByIdAndUpdate
        const updatedUser = yield user_model_1.User.findByIdAndUpdate(id, { isActive }, { new: true }).select('-password');
        if (!updatedUser) {
            return res.status(404).json({
                success: false,
                message: 'Không tìm thấy người dùng sau khi cập nhật'
            });
        }
        console.log(`✅ [Admin] User status updated for: ${user.name}`);
        res.json({
            success: true,
            message: `Đã ${isActive ? 'kích hoạt' : 'vô hiệu hóa'} tài khoản người dùng`,
            data: {
                userId: id,
                isActive,
                updatedAt: user.updatedAt
            }
        });
    }
    catch (err) {
        console.error('❌ [Admin] Error updating user status:', err);
        res.status(500).json({
            success: false,
            message: 'Lỗi khi cập nhật trạng thái người dùng',
            error: err.message
        });
    }
});
exports.updateUserStatus = updateUserStatus;
// PUT /admin/users/:id/role - Cập nhật vai trò người dùng
const updateUserRole = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { id } = req.params;
        const { role } = req.body;
        console.log(`🔄 [Admin] Updating user role for ID: ${id}, role: ${role}`);
        if (!['user', 'admin'].includes(role)) {
            return res.status(400).json({
                success: false,
                message: 'Vai trò không hợp lệ'
            });
        }
        const user = yield user_model_1.User.findById(id);
        if (!user) {
            return res.status(404).json({
                success: false,
                message: 'Không tìm thấy người dùng'
            });
        }
        user.role = role;
        user.updatedAt = new Date();
        yield user.save();
        console.log(`✅ [Admin] User role updated for: ${user.name} to ${role}`);
        res.json({
            success: true,
            message: `Đã cập nhật vai trò người dùng thành ${role === 'admin' ? 'Quản trị viên' : 'Người dùng'}`,
            data: {
                userId: id,
                role,
                updatedAt: user.updatedAt
            }
        });
    }
    catch (err) {
        console.error('❌ [Admin] Error updating user role:', err);
        res.status(500).json({
            success: false,
            message: 'Lỗi khi cập nhật vai trò người dùng',
            error: err.message
        });
    }
});
exports.updateUserRole = updateUserRole;
// DELETE /admin/users/:id - Xóa người dùng
const deleteUser = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { id } = req.params;
        console.log(`🗑️ [Admin] Deleting user ID: ${id}`);
        const user = yield user_model_1.User.findById(id);
        if (!user) {
            return res.status(404).json({
                success: false,
                message: 'Không tìm thấy người dùng'
            });
        }
        // Prevent deleting admin users
        if (user.role === 'admin') {
            return res.status(403).json({
                success: false,
                message: 'Không thể xóa tài khoản quản trị viên'
            });
        }
        const userName = user.name;
        yield user_model_1.User.findByIdAndDelete(id);
        console.log(`✅ [Admin] User deleted: ${userName}`);
        res.json({
            success: true,
            message: `Đã xóa người dùng: ${userName}`,
            data: {
                deletedUserId: id,
                deletedUserName: userName
            }
        });
    }
    catch (err) {
        console.error('❌ [Admin] Error deleting user:', err);
        res.status(500).json({
            success: false,
            message: 'Lỗi khi xóa người dùng',
            error: err.message
        });
    }
});
exports.deleteUser = deleteUser;
// GET /admin/online-status - Lấy thông tin trạng thái online của tất cả users
const getOnlineStatus = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        console.log('🟢 [Activity] Getting online status...');
        const onlineInfo = (0, activity_tracker_1.getOnlineUsersInfo)();
        const onlineUserIds = (0, activity_tracker_1.getOnlineUserIds)();
        // Get user details for online users
        const onlineUsers = yield user_model_1.User.find({
            _id: { $in: onlineUserIds }
        }).select('name email role').lean();
        console.log(`🟢 [Activity] Online users: ${onlineInfo.count}`);
        console.log(`🟢 [Activity] Online user IDs: ${onlineUserIds.join(', ')}`);
        res.json({
            success: true,
            data: {
                totalOnline: onlineInfo.count,
                onlineUserIds,
                onlineUsers,
                detailedInfo: onlineInfo.users
            },
            message: `Hiện có ${onlineInfo.count} người dùng đang online`
        });
    }
    catch (err) {
        console.error('❌ [Activity] Error getting online status:', err);
        res.status(500).json({
            success: false,
            message: 'Lỗi khi lấy thông tin trạng thái online',
            error: err.message
        });
    }
});
exports.getOnlineStatus = getOnlineStatus;
