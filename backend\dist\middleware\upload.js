"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const multer_1 = __importDefault(require("multer"));
const path_1 = __importDefault(require("path"));
const fs_1 = __importDefault(require("fs"));
const uuid_1 = require("uuid");
// <PERSON><PERSON><PERSON> bả<PERSON> thư mục uploads tồn tại
const uploadDir = path_1.default.join(__dirname, '../uploads');
if (!fs_1.default.existsSync(uploadDir)) {
    fs_1.default.mkdirSync(uploadDir, { recursive: true });
}
// Cấu hình storage cho multer
const storage = multer_1.default.diskStorage({
    destination: function (req, file, cb) {
        try {
            console.log('📁 [Upload] Processing file upload for path:', req.path, 'method:', req.method);
            // Tạo thư mục riêng cho chiến dịch nếu đang tạo mới hoặc cập nhật
            if (req.path.includes('/admin') && (req.method === 'POST' || req.method === 'PUT')) {
                const campaignId = req.method === 'POST' ? (0, uuid_1.v4)() : req.params.id || (0, uuid_1.v4)();
                const campaignDir = path_1.default.join(uploadDir, campaignId);
                console.log('📁 [Upload] Creating/using campaign directory:', campaignDir, 'for method:', req.method);
                fs_1.default.mkdirSync(campaignDir, { recursive: true });
                if (req.method === 'POST') {
                    req.body.campaignId = campaignId; // Chỉ set campaignId cho POST
                }
                cb(null, campaignDir);
            }
            // Nếu đang cập nhật, sử dụng thư mục của chiến dịch đó
            else if (req.path.startsWith('/admin/') && req.method === 'PUT') {
                const campaignId = req.params.id;
                const campaignDir = path_1.default.join(uploadDir, campaignId);
                console.log('📁 [Upload] Using existing campaign directory:', campaignDir);
                if (!fs_1.default.existsSync(campaignDir)) {
                    fs_1.default.mkdirSync(campaignDir, { recursive: true });
                }
                cb(null, campaignDir);
            }
            // Mặc định sử dụng thư mục uploads
            else {
                console.log('📁 [Upload] Using default upload directory:', uploadDir);
                cb(null, uploadDir);
            }
        }
        catch (error) {
            console.error('❌ [Upload] Error in destination function:', error);
            cb(error, uploadDir); // Fallback to default directory
        }
    },
    filename: function (req, file, cb) {
        // Tạo tên file duy nhất với timestamp và uuid
        const uniqueSuffix = Date.now() + '-' + (0, uuid_1.v4)();
        cb(null, file.fieldname + '-' + uniqueSuffix + path_1.default.extname(file.originalname));
    }
});
// Kiểm tra file type
const fileFilter = (req, file, cb) => {
    console.log('🔍 [Upload] Checking file type:', file.mimetype, 'for file:', file.originalname);
    // Chỉ chấp nhận các file ảnh
    if (file.mimetype.startsWith('image/')) {
        console.log('✅ [Upload] File type accepted:', file.mimetype);
        cb(null, true);
    }
    else {
        console.log('❌ [Upload] File type rejected:', file.mimetype);
        cb(new Error('Chỉ chấp nhận file ảnh!'));
    }
};
// Cấu hình multer
const upload = (0, multer_1.default)({
    storage: storage,
    fileFilter: fileFilter,
    limits: {
        fileSize: 5 * 1024 * 1024, // Giới hạn 5MB
        files: 5 // Tối đa 5 file
    }
});
exports.default = upload;
