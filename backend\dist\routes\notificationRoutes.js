"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const notificationController_1 = require("../controllers/notificationController");
const authMiddleware_1 = require("../middleware/authMiddleware");
const router = express_1.default.Router();
// Get unread notifications count (no auth required for polling)
router.get('/unread-count', authMiddleware_1.protect, notificationController_1.getUnreadCount);
// Apply protect middleware to routes that need authentication
// Get user's notifications
router.get('/', authMiddleware_1.protect, notificationController_1.getUserNotifications);
// Mark notification as read
router.patch('/:notificationId/read', authMiddleware_1.protect, notificationController_1.markAsRead);
// Mark all notifications as read
router.patch('/read-all', authMiddleware_1.protect, notificationController_1.markAllAsRead);
// Route DELETE /:id sẽ tạm thời bị loại bỏ vì không có hàm controller tương ứng
exports.default = router;
